/* eslint-disable no-nested-ternary */
import { isEmpty as lodashIsEmpty, get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import AppState from '@c2x/apis/AppState';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import Page, { IBasePageProps } from '@c2x/components/Page';
import Loading from '@c2x/apis/Loading';
import Event from '@c2x/apis/Event';
import MiniChannel from '@miniChannel';
import classnames from 'classnames';
import React, { memo } from 'react';
import {
  xMergeStyles,
  XView as View,
  xRouter,
  xApplication as Application,
  xShowToast,
  XViewExposure,
} from '@ctrip/xtaro';
import { Block } from '@tarojs/components';
import memoize from 'memoize-one';
import { CouponList } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { DetailReqType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailReqDtoType';
import {
  ResInfoType,
  ResultInfoType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/CreateOrderResponseType';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import { layout, color, space, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
// #ifdef mini
import MiniDepositModal from '@miniComponents/depositModal';
// #endif
import {
  LabelsType,
  FeeItemsType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/PriceResponseDtoType';
import {
  Certificate,
  Passenger,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import { RentalGuaranteeV2Type } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import DepositInfo from '@miniComponents/depositInfo/depositInfoContainer';
// #ifndef alipay
import ZhimaIntroModal from '@miniComponents/zhimaIntro';
// #endif
// #ifdef alipay
import SesameFeedback from '@miniComponents/sesameFeedback';
import qunarUtils from '@/common/utils/util';
// #endif
import c2xStyles from './booking.module.scss';
import {
  DepositPayInfosType,
  PromptInfoType,
  MembershipPerceptionType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import { DriverItem, IInputType } from '../../ComponentBusiness/BookForm';
// #ifndef mini
import {
  ModifyOrderModal,
  TitleHightlightType,
} from '../../ComponentBusiness/OrderConfirmModal/Index';
// #endif
import RebookTip from '../../ComponentBusiness/Tips/src/RebookTip';
import {
  rebookPenaltyTipText,
  rebookApplyPenaltyTip,
  rebookTipText,
} from '../../Constants/CommonTexts';
import { PageRole } from '../../Constants/CommonEnums';
// #ifndef weapp
import CancelZhimaModal from '../../Containers/BookingCancelZhimaModalContainer';
// #endif
import CPage, { IStateType } from '../../Components/App/CPage';
// #ifndef mini
import BookFooterContainer from '../../Containers/OSD/BookFooterContainer';
// #endif
import Insurance from '../../Containers/BookingInsuranceContainer';
import Extras from '../../Containers/ExtrasInfoContainer';
import BookingFooter from '../../Containers/BookingFooterContainer';
import GpdrContainer from '../../Containers/GpdrContainer';
import BookingModalsContainer from '../../Containers/BookingModalsContainer';
// #ifndef weapp
import DepositPaymentContainer from '../../Containers/DepositPaymentContainer';
// #endif
import BookingCouponAndDepositContainer from '../../Containers/BookingCouponAndDepositContainer';
import { AssistiveTouch } from '../../Components/Index';

import {
  User,
  CarStorage,
  CarLog,
  Utils,
  CarABTesting,
  AppContext,
  InsuranceConfirmUtil,
  Channel,
  CarFetch,
  EventHelper,
  GetABCache,
  CarServerABTesting,
} from '../../Util/Index';
import { logValidate } from '../../Util/Log/ToastLog';
import { MiddlePay } from '../../Util/Payment/Index';
// import SesameContainer from '../../Containers/SesameContainer';
// #ifndef mini
import DepositMethod from '../../Containers/BookingDepositMethodContainer';
// #endif
import BookingFormContainer from '../../Containers/BookingFormContainer';
import BusinessLicenseModal from '../../Containers/BusinessLicenseModalContainer';
// #ifndef mini
import FuelDescriptionModalContainer from '../../Containers/FuelDescriptionModalContainer';
// #endif
import BookingConfirmModal from '../../Containers/BookingConfirmModalContainer';
// 小程序业务差异化：解耦集合引用
import VendorListOptimizationStrengthenModal from '../../Containers/VendorListOptimizationStrengthenModalContainer';
import EtcIntroModalContainer from '../../Containers/EtcIntroModalContainer';
// #ifndef mini
import DepositRateDescriptionModal from '../../Containers/BookingDepositRateDescriptionModalContainer';
import FlightDelayRulesModal from '../../Containers/BookingFlightDelayRulesModalContainer';
// #endif
import {
  StorageKey,
  Platform as PlatformRouter,
  LogKey,
  EventName,
  Enquiry,
  Document,
  ImageUrl,
  LogKeyDev,
  UITestID,
} from '../../Constants/Index';

import {
  getProductReq,
  getProductMapWithHandleCache,
  getBookingFirstScreenParam,
  getProductRequestReference,
  getBaseResData,
} from '../../Global/Cache/ProductSelectors';
import {
  ProductSelectors,
  ProductReqAndResData,
  listStatusKeyList,
} from '../../Global/Cache/Index';
import {
  getRegisterPageData,
  setListStatusData,
} from '../../Global/Cache/ListReqAndResData';
import {
  LogBookQueryPrice,
  LogPaymentCallback,
  LogProductInfo,
} from '../../State/Product/UBTLog';
import { PriceTimer } from '../../State/Product/Model';
import {
  isCreditRentPayType,
  getGuidePageParam,
  getRentalGuaranteeV2,
  getFeeDetailData,
} from '../../State/Product/BbkMapper';
import { ProductReq } from '../../State/Product/FuntionTypes';
import {
  // #ifndef mini
  VehicleLocation,
  Advantage,
  VehicleWrapper,
  Cancellation,
  InvoiceInfo,
  // #endif
  NoMatch,
  BookingWrapper,
  FormWrapper,
} from './Components';
import Header from '../../Containers/Book/BookHeaderContainer';
import AdvantageInfo from './Component/Advantage';
import { CarPayParams } from '../../Types/PaymentType';
import {
  OSD_NORMAL_SEQUENCE,
  IBU_SEQUENCE,
  OSD_SEQUENCE,
  ISD_SEQUENCE,
} from '../../State/Booking/Types';
import { ShowPayModeType } from '../../State/Product/Enums';
import { Enums } from '../../ComponentBusiness/Common';
import { isCreditRent } from '../../Util/ABTesting';
import { ButtonAction } from '../../Components/CouponPreValidationModals/Index';
import StoreModal from '../../ComponentBusiness/OptimizeStoreModal';
// #ifndef weapp alipay 小程序业务差异化： 小程序注释所有booking交易快照截图相关
import ProductSnapShotContainer from '../../Containers/ProductSnapShotContainer';
import {
  getSnapImageData,
  getSnapShotData,
  getBookingSnapImageData,
} from './SnapShot';
// #endif
import { ISelectedIdType } from '../Product/Types';
import texts from './Texts';
// #ifndef mini
import { modifyOrderModalText } from '../ModifyOrderConfirm/Texts';
// #endif
import BookingPriceChangePop from '../../Containers/BookingPriceChangePopContainer';
// #ifndef mini
import BookingCreateInsLoading from '../../Containers/BookingCreateInsLoadingContainer';
import BookingCreateInsFailPop from '../../Containers/BookingCreateInsFailPopContainer';
// #endif
// #ifndef mini
import Distance from '../../Containers/BookingDistanceContainer';
// #endif
import ProductConfirmModalNew from '../../Containers/ProductConfirmModalContainerNew';
import BookingEasyLifeModal from '../../Containers/BookingEasyLifeModalContainer';
import BookingPriceDetail from '../../Containers/BookingPriceDetailContainer';

import { BuildInsuranceParamsRequestType } from '../../Constants/Types/InsuranceConfirmDtoType';
import ValidatePassenger, {
  IDriversMapType,
} from '../../ComponentBusiness/DriverAddeditModal/src/Validate';
import AddInstructModal from './AddInstructModals';
import ApplyPenaltyInputModal from './ApplyPenaltyInputModal';
// #ifndef mini
import ApproveExplainModal, { ApproveExplainType } from './ApproveExplainModal';
// #endif
import HalfPageModal from './BookingHalfPageModal';
import EhiFreeDepositRuleModalContainer from '../../Containers/EhiFreeDepositRuleModalContainer';
// #ifndef mini
import FreeDepositRuleModalContainer from '../../Containers/FreeDepositRuleModalContainer';
// #endif
// import { OnAuthenticationDateType } from '../../State/Sesame/Types';
import { IInstalmentInfo } from '../../ComponentBusiness/Naquhua/src/Types';
import ServiceClaimMoreModal from '../../ComponentBusiness/ServiceClaimMoreModal';
import CarServiceDetailModal from '../../ComponentBusiness/CarServiceDetailModal';
// #ifndef mini
import LocalContactsModal from '../../Containers/BookLocalContactsModalContainer';
// #endif
import { getImAddress } from '../../Global/IM/IM';
import BookingLoading from './Component/Loading';
// #ifndef mini 小程序业务差异化：18631/AddSnapShot未使用redux注释
import BookingSnapShot from './Component/SnapShot';
// #endif
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import PickupDownGradePop from '../../Containers/PickupDownGradePopContainer';
import SkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';
import { CarServiceFromPageTypes } from '../../ComponentBusiness/CarService/src/Types';
import PriceAlert from './Component/PriceAlert';
import { QConfigType } from '../../Types/Dto/QConfigResponseType';
import { PayScene, PayType } from '../../Constants/PayEnums';
// #ifndef mini
import ModifyOrderInfoExplain, {
  ModifyOrderInfoExplainOSD,
} from '../../ComponentBusiness/ModifyOrderInfoExplain/Index';
// #endif
import BlockCard from './Component/BlockCard';
import CouponModal from '../../ComponentBusiness/Coupon/index';
import { initializeABBookingPage } from '../../Util/CarABTesting/InitializeAB';
// #ifndef mini
import { PriceDetailModalOsd } from '../Product/Components/Index';
import { PersonalAuthCheckModal } from '../../ComponentBusiness/PersonalInfoAuthCheck';
import CancelRule, { CancelRuleType } from '../../ComponentBusiness/CancelRule';
// #endif
import ErrorKey from '../../Constants/ErrorKey';
import { getSelfServiceLogData } from '../List/Method';
// #ifndef mini
import NewCouponAndActivity from './Component/NewCouponAndActivity';
// #endif
import { DriverLicense } from './Types';
import BookingVehicleAndVendorInfoVCContainer from '../../Containers/BookingVehicleAndVendorInfoVCContainer';
// #ifndef mini
import { MarketingFooter } from '../Home/Components/Index';
import ExtrasInfoV2Container from '../../Containers/ExtrasInfoV2Container';
// #endif
import DepositTip from './Component/DepositTip';
// #ifndef mini
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
// #endif
import {
  ContactType,
  LocalContactInfoType,
} from '../../Constants/LocalContactsData';
import AsMobileBlock from '../../ComponentBusiness/AsMobileBlock';
import { getRequestId } from '../../Global/Cache/ListResSelectors';
import { __global } from '../../Diff/platform';
import {
  ORDER_DETAIL_PATH,
  QUNAR_H5_DOMAIN,
} from '../../Diff/constants/platform';
import FixedTopBar from '../../Diff/component/fixedTopBar';
import { getTopBarInfo } from '../../Diff/component/immersiveTopBar/utils';
import BottomWrap from '../../Diff/component/bottomWrap/bottomWrap';
import { AdjustPriceDetail } from '../../Types/Dto/QueryPriceInfoType';

const { DepositPayType } = Enums;

export interface PropsType extends IBasePageProps {
  isRebook: boolean;
  isRebookOsd: boolean;
  hasBookingConfirmInfo: boolean;
  toolBoxCustomerJumpUrl: string;
  ctripOrderId?: string | number;
  resCancelFeeRebook?: any;
  setRebookPenalty: (data) => void;
  fetchQueryCancelFeeRebook: (data) => void;
  resetCancelFeeRebook: () => void;
  rebookPenalty?: string;
  config: QConfigType;
  addInstructData: {
    title: string;
    content: string;
  };
  positivePolicies: Array<LabelsType>;
  preLicensData: any;
  activityDetail: any;
  adjustPriceDetail: AdjustPriceDetail;
  passenger: Passenger;
  passengerIDCard: Certificate;
  isEasyLife: boolean;
  confirmInfo: FeeItemsType;
  cancelRuleInfo: FeeItemsType;
  invoiceInfo: any;
  couponList: CouponList;
  driverInfo: DriverItem[];
  orderData: ResInfoType;
  payParams: CarPayParams;
  needFlightNo: boolean;
  isMaskLoading: boolean;
  hasVehicleConfig: boolean;
  isOnlyCreditCard: boolean;
  orderId: number;
  payAmount: number;
  showPayMode: ShowPayModeType;
  materialModalRef: any;
  storePolicyRef: any;
  easyLifePopVisible: boolean;
  passengerList: Array<Passenger>;
  isMergeDeposit: boolean;
  checkFlightNoLoading: boolean;
  createOrder: (data?: {
    isCheckOrder?: boolean;
    inverseInsuranceIds?: Array<string | number>;
    insuranceAgentToken?: string;
    payRequestId?: string;
    isSecretBox?: boolean;
    isContractTemplates?: boolean;
  }) => void;
  changeFormData: (data: DriverItem[]) => void;
  changeModalStatus: (data) => void;
  // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
  saveSnapshot: (data) => void;
  // #endif
  selectDriver: (data) => void;
  queryPriceInfo: () => void;
  initSesameAuthState: () => void;
  onPressEasyLife: (visible: boolean) => void;
  clear: () => void;
  theme?: any;
  authenStatusTicket?: string;
  isLogin?: boolean;
  flightDelayRules?: any;
  queryProduct: (data) => void;
  reference?: any;
  isDebugMode?: boolean;
  payRequestId?: string;
  selectedIdType?: ISelectedIdType;
  // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
  isSnapShotFinish?: boolean;
  // #endif
  noNeedRefresh?: boolean;
  isFail?: boolean;
  isOnMask?: boolean;
  isPriceLoading?: boolean;
  depositPayType?: number;
  curDepositPayInfo?: DepositPayInfosType;
  isCtripCreditRent?: boolean;
  productReq?: ProductReq;
  productRes?: any;
  curInsPackageId?: number;
  isShowPriceConfirm: boolean; // 是否展示CarDialog
  showPriceConfirm?: () => void;
  supplierModalVisible: boolean;
  depositIntroduceModalVisible: boolean;
  productRentalLocationInfo: any;
  isProductLoading: boolean;
  bookPriceTrackInfo?: Object;
  resetLoading?: (data: boolean, option?: any) => void;
  maskerDriver?: () => void;
  unMaskerDriver?: () => void;
  payMode?: number;
  priceVersion?: string;
  selectedInsuranceId?: Array<string>;
  insConfirmReqParam: BuildInsuranceParamsRequestType;
  createInsLoadingPopVisible: boolean;
  setPriceChangePopIsShow: (data: boolean) => void;
  setCreateInsLoadingIsShow: (data: boolean) => void;
  setCreateInsFailPopIsShow: (data: boolean) => void;
  setInsRemindPopIsShow: (data: boolean) => void;
  refreshList: () => void;
  retry: (data) => void;
  insConfirmCallBack: (data) => void;
  priceChangeCode?: string;
  driversMap?: IDriversMapType;
  needValidateOrder?: boolean;
  ctripRentNeedValidateOrder?: boolean;
  isPriceFail?: boolean;
  yongAge?: number;
  oldAge?: number;
  authStatus?: number;
  onAuthentication?: (param: any) => void;
  ehiNoteInfo?: PromptInfoType;
  ehiFreeDepositModalVisible?: boolean;
  preferentialTips?: any;
  validateIsDownGrade: (data) => void;
  needDownGrade?: boolean;
  cancelModalData?: any;
  setVendorListModalData?: (data: any) => void;
  iousInfo: IInstalmentInfo;
  selectedLoanPayStageCount: string;
  setSelectedLoanPayStageCount: (data: string) => void;
  setPassengerError: (isError: boolean) => void;
  setCouponPreValidationModalVisible: (
    visible: boolean,
    content: ResultInfoType,
  ) => void;
  isNaquhuaValidated: boolean;
  rentCenterId: any;
  dropOffRentCenterId: any;
  membershipPerception: MembershipPerceptionType;
  addSaleOutList: (productKey: string) => void;
  addRecommendSaleOutList: (productKey: string) => void;
  firstScreenParam?: any;
  createOrderFailModalVisible?: boolean;
  uniqueOrderModalVisible?: boolean;
  flightErrorModalVisible?: boolean;
  dayGap?: number;
  addOnCodes?: string[];
  changeSelectInsurance?: (data) => void;
  ptime?: string;
  rtime?: string;
  setLogInfo?: (data) => void;
  isPriceTimerLoading?: boolean;
  modifyOrderDesc?: string;
  fromPage?: string;
  setVendorPriceData: (data) => void;
  vendorPriceInfo: any;
  changeCoupon: (coupon: any) => void;
  isSecretBox?: boolean;
  agreeSubmitName: string;
  isKlb?: boolean;
  setFlightDelayRulesModalVisible: (data: boolean) => void;
  depositRateDescriptionModalVisible: boolean;
  setDepositRateDescriptionModalVisible: (data: boolean) => void;
  currenctTotalPrice?: number;
  driverLicenseItems?: Array<DriverLicense>;
  curDriverLicense?: DriverLicense;
  selectCurDriverLicense?: (data) => void;
  // #ifndef weapp alipay 小程序业务差异化：18631/queryLicencePolicy未使用redux注释
  queryLicencePolicy?: (data) => void;
  // #endif
  setBusinessLicenseModalVisible?: (data) => void;
  isBusinessLicenseModalVisible?: boolean;
  setEhiFreeDepositModalVisible?: (data) => void;
  isRefactor?: boolean;
  isEasyLife2024?: boolean;
  hasExtrasProducts?: boolean;
  isFuelDescriptionModalVisible?: boolean;
  setFuelDescriptionModalVisible?: (data: boolean) => void;
  queryEquipmentInfo?: (params) => void;
  osdModifyOrderNote: any;
  setRebookParamsOsd: (data: any) => void;
  pickUpAreaCode?: string;
  // #ifndef weapp alipay 小程序业务差异化：13609/getCountries未使用redux注释
  queryCountrysInfo?: () => void;
  // #endif
  changeLocalContactsData?: (data: LocalContactInfoType[]) => void;
  localContactsData?: LocalContactInfoType[];
  optionalContactMethods?: LocalContactInfoType[];
  logBaseInfo?: any;
  newRecommendType?: any;
  setEtcIntroModal?: (data: any) => void;
  isEtcIntroModalVisible?: boolean;
  isHasEtcIntroModal?: boolean;
  depositInfo?: any;
  depositPayInfos?: any;
  productDepositInfo?: any;
  promptInfos?: any;
  zhimaFlag?: any;
  sesameGoAuth?: any;
  orderPriceInfo?: any;
  // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
  isTecentAds?: boolean;
  tecentAdsClickedStatus?: Record<string, boolean>;
  setTecentAdsClickedStatus?: (key: string, value: boolean) => void;
  // #endif
  // #ifndef mini 小程序业务差异化：列表缩短预订流程
  refreshReducePathSoldOut?: () => void;
  // #endif
}
interface StateType extends IStateType {
  opacity: number;
  isLogin: boolean;
  isFinishRender: boolean;
  // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
  isSnapShotRender: boolean;
  getSnapImageFinish: boolean;
  // #endif
  addInstructModalVisible: boolean;
  miniDepositModalVisible: boolean;
  zhimaModalVisible: boolean;
  approveExplainModalVisible: boolean;
  approveExplain: ApproveExplainType;
  isApproveExplainLoading?: boolean;
  productConfirmModalVisible: boolean;
  driverLicenseModalVisible?: boolean;
  optimizationStrengthenModalVisible: boolean;
  priceDetailModalVisible: boolean;
  osdPriceDetailModalVisible: boolean;
  isShowModifyOrderModal: boolean;
  isShowOptimizeStoreModal: boolean;
  activityNoteModalVisible: boolean;
  adjustPriceNoteModalVisible: boolean;
  productConfirmAnchor: LayoutPartEnum;
  serviceClaimMoreVisible: boolean;
  carServiceDetailVisible: boolean;
  showServiceDetailCode: string;
  isShowApplyPenaltyInputModal: boolean;
  isShowCouponModal: boolean;
  personalInfoChecked: boolean;
  personalInfoAuthModalVisible: boolean;
  showConfirm: boolean;
  extrasInfoVisible: boolean;
  isShowTableArrow: boolean;
  keyboardHeight: number;
  localContactsModalVisible: boolean;
  localContactsInputIsFocus: boolean;
  reference: any;
  showZhimaModal: boolean;
  currentActivityCode: string;
}
const {
  vh,
  vw,
  ensureFunctionCall,
  getPixel,
  fixOffsetTop,
  uuid,
  fixIOSOffsetBottom,
  adaptNoaNomalousBottom,
  isHarmony,
  isIos,
} = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;
const styles = StyleSheet.create({
  shadow: {
    // @ts-expect-error
    shadowOffset: { width: 0, height: getPixel(-5) },
    shadowRadius: getPixel(5),
    shadowColor: color.black,
    shadowOpacity: 0.05,
    elevation: 20,
  },
  priceDetailStyle: {
    bottom: getPixel(120) + fixIOSOffsetBottom() + adaptNoaNomalousBottom(),
  },
  loadingSpace: {
    marginTop: space.verticalXXL,
    marginBottom: space.verticalXXL,
  },
  modifyRuleExplainWrap: {
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    marginBottom: getPixel(16),
  },
  osdModifyRuleExplainWrap: {
    paddingRight: getPixel(32),
    paddingLeft: getPixel(46),
    paddingTop: getPixel(40),
    borderRadius: 0,
    backgroundColor: color.C_fafcff,
  },
  freeCancelLoadingBg: {
    overflow: 'hidden',
    marginBottom: getPixel(-25),
    marginTop: getPixel(16),
  },
  freeCancelLoadingBgHeight: {
    height: getPixel(66),
  },
  bookingOptimizationLoadingSpace: {
    marginTop: space.verticalXXL,
    marginBottom: space.verticalXXL,
    width: vw(100) - getPixel(48),
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
  },
  bookingOptimizationButtonTextStyle: {
    ...font.title4Style,
  },
  bookingOptimizationRelievedBookingMtNew: {
    backgroundColor: color.C_EDF2F8,
  },
  extrasInfo: {
    marginBottom: 0,
    marginTop: getPixel(16),
  },
  footBarWrap: {
    position: 'relative',
    // 小程序业务差异化： 提高底部bar层级，为了防止费用明细弹层盖过底部bar
    zIndex: MiniChannel.isMini() ? 99 : 8,
  },
});

interface DepositPaymentProps {
  onPressDriver: () => void;
}
// #ifndef weapp
const depositPaymentComponentConfig = memoize(fixIsCreditRent => ({
  [fixIsCreditRent && PlatformRouter.COMPONENT_CHANNEL.ISD]: memo(
    ({ onPressDriver }: DepositPaymentProps) => (
      <DepositPaymentContainer
        title={texts.depositPaymentTitle}
        rightBtnText={texts.giveupDeposit}
        onPressDriver={onPressDriver}
        isBooking={true}
      />
    ),
  ),
}));
// #endif
// #ifndef mini
const getOsdCancelRule = (cancelRuleInfo: FeeItemsType, logBaseInfo) => {
  if (!Utils.isCtripOsd) return null;
  if (cancelRuleInfo?.items?.length > 0) {
    const { title, subTitle, items = [], description } = cancelRuleInfo || {};
    return (
      <CancelRule
        title={title}
        subTitle={subTitle}
        items={items}
        type={CancelRuleType.Booking}
        description={description}
        testID={CarLog.LogExposure({
          name: '曝光_填写页_取消政策',

          info: logBaseInfo,
        })}
      />
    );
  }
  return (
    <Cancellation
      cancelRuleInfo={cancelRuleInfo}
      testID={CarLog.LogExposure({ name: '曝光_填写页_取消政策模块' })}
    />
  );
};
// #endif

class Booking extends CPage<PropsType, StateType> {
  authenStatusTicket = '';

  authLogin = false;

  materialModalRef = null;

  storePolicyRef = null;

  // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
  snapImageData = null;

  snapTime = 0;
  // #endif

  insConfirmData = {};

  // 统计跳转保险页面时长
  isForwardInsPage = false;

  insActiveTime = 0;

  insuranceEndTime = 0;

  priceTimer = null;

  createPiceTimerBySelf = false;

  // 控制 pageDidAppear 刷新价格
  // 只有提交订单后才需刷新
  needRefreshPrice = false;

  personalInfoChecked = false;

  isShowConfirmed = false;

  backgroundTime = 0;

  appState = AppState.currentState;

  keyboardWillShowListener = null;

  keyboardWillHideListener = null;

  appStateActiveCallBack = null;

  appStateBackgroundCallBack = null;

  // #ifdef mini
  topBarInfo: { topBarHeight: number };
  // #endif

  constructor(props) {
    super(props);
    this.state = {
      opacity: 0,
      isLogin: false,
      isFinishRender: false,
      // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
      isSnapShotRender: false,
      getSnapImageFinish: false,
      // #endif
      addInstructModalVisible: false,
      miniDepositModalVisible: false,
      zhimaModalVisible: false,
      approveExplainModalVisible: false,
      approveExplain: {},
      isApproveExplainLoading: true,
      productConfirmModalVisible: false,
      driverLicenseModalVisible: false,
      optimizationStrengthenModalVisible: false,
      priceDetailModalVisible: false,
      osdPriceDetailModalVisible: false,
      isShowModifyOrderModal: false,
      isShowOptimizeStoreModal: false,
      productConfirmAnchor: null,
      activityNoteModalVisible: false,
      adjustPriceNoteModalVisible: false,
      serviceClaimMoreVisible: false,
      carServiceDetailVisible: false,
      showServiceDetailCode: '',
      isShowApplyPenaltyInputModal: false,
      isShowCouponModal: false,
      personalInfoChecked: false,
      personalInfoAuthModalVisible: false,
      showConfirm: false,
      extrasInfoVisible: false,
      isShowTableArrow: false,
      keyboardHeight: 0,
      localContactsModalVisible: false,
      localContactsInputIsFocus: false,
      reference: null,
      showZhimaModal: false,
      currentActivityCode: '',
    };
    // 填写页实验批量初始化
    initializeABBookingPage();
    this.authenStatusTicket = props.authenStatusTicket;
    this.authLogin = props.isLogin;
    this.onScroll = this.onScroll.bind(this);
    this.onLayoutDriver = this.onLayoutDriver.bind(this);
    this.handleBookPress = this.handleBookPress.bind(this);
    this.scrollView = React.createRef();
    this.materialModalRef = React.createRef();
    this.storePolicyRef = React.createRef();
    this.priceDetailsRef = React.createRef();
    this.insuranceSuitsModalRef = React.createRef();
    this.onPressDriver = this.onPressDriver.bind(this);
    this.onPressBar = this.onPressBar.bind(this);
    this.onPressTandC = this.onPressTandC.bind(this);
    this.onPressCoupon = this.onPressCoupon.bind(this);
    this.onConfirmFightNo = this.onConfirmFightNo.bind(this);
    this.onPressFlightDelayRules = this.onPressFlightDelayRules.bind(this);
    this.queryProduct = this.queryProduct.bind(this);
    this.priceTimer = props.priceTimer;
    // insuranceRequestId & insuranceSelectedIds 赋值时机
    // 进入填写页时赋值，标识一次填写页内的操作
    AppContext.setInsuranceRules(props.selectedInsuranceId);
    // 缓存供应商报价信息，用于 Product 接口请求
    const { setVendorPriceData, vendorPriceInfo } = this.props || {};
    setVendorPriceData(vendorPriceInfo);

    if (!isHarmony && Utils.isCtripIsd()) {
      this.queryProduct(false, true);
      this.initLogInfo();
    }
    this.initListData();
    this.isShowConfirmed = false;
    if (Utils.isCtripOsd()) {
      // 同步已缓存的AB实验结果
      GetABCache.syncCacheAb();
    }
    this.keyboardWillShowListener =
      isIos &&
      Keyboard.addListener(
        'keyboardWillShow',
        this.keyboardWillShow.bind(this),
      );

    this.keyboardWillHideListener =
      isIos &&
      Keyboard.addListener(
        'keyboardWillHide',
        this.keyboardWillHide.bind(this),
      );
    // #ifdef mini
    this.topBarInfo = {
      topBarHeight: getTopBarInfo().topBarHeight,
    };
    // #endif
  }

  keyboardWillShow(e) {
    this.setState({
      keyboardHeight: e.endCoordinates.height,
    });
  }

  keyboardWillHide() {
    this.setState({
      keyboardHeight: 0,
    });
  }

  priceDetailsRef: any;

  insuranceSuitsModalRef: any;

  scrollView: any;

  dirverY: number;

  depositPayY: number;

  osdDepositPayY: number;

  couponY: number;

  couponHeight: number;

  disableBookingFlag: boolean;

  insConfirmInsList: Array<any>;

  /* eslint-disable class-methods-use-this */
  // do not use static for Log pV
  getPageId() {
    return Channel.getPageId().Book.ID;
  }

  getPageParamInfo() {
    const info: any = {};
    const {
      reference = {},
      rentCenterId,
      dropOffRentCenterId,
      vendorPriceInfo,
      isSecretBox,
    } = this.props;
    const { isdParam, pHub, rHub } = reference;

    if (isdParam) {
      info.vehicledata = {
        vehiclecode: isdParam.vpid,
        vehicleName: isdParam.vehicleName,
        vendorCode: isdParam.vendorid,
        vendorName: isdParam.csname,
        storeCode: isdParam.psid,
        groupId: isdParam.groupId,
        ispickupStation: Utils.getLogStrValue(pHub),
        isdropoffStation: Utils.getLogStrValue(rHub),
        ispickupSource: Utils.getLogStrValue(rentCenterId),
        isdropoffSource: Utils.getLogStrValue(dropOffRentCenterId),
        allTags: vendorPriceInfo?.allTags,
        isSelfService: getSelfServiceLogData(vendorPriceInfo?.allTags),
        iseasyLife2024: !!isdParam.isEasyLife2024,
      };
    }
    // 盲盒曝光埋点，1盲盒活动 0常规活动
    info.actType = isSecretBox ? '1' : '0';
    return info;
  }

  onScroll(event: any) {
    const { opacity } = this.state;
    const { nativeEvent } = event;
    const scrollY = nativeEvent.contentOffset.y;
    if (Utils.isCtripIsd()) {
      if (scrollY > 540) {
        this.setState({ isShowTableArrow: true });
      }
      return;
    }

    let curOpacity = Math.floor(scrollY) / 50;
    if (curOpacity > 1) curOpacity = 1;
    if (curOpacity < 0) curOpacity = 0;
    if (opacity !== curOpacity) {
      this.setState({ opacity: curOpacity });
    }
  }

  queryProduct(isLoginRefresh?: boolean, isStartPriceTimer?: boolean) {
    const productParams: DetailReqType = getProductReq();
    const reference = productParams && productParams.reference;
    const { queryProduct } = this.props;
    if (Utils.isCtripIsd()) {
      queryProduct({ reset: true, isStartPriceTimer });
    } else {
      queryProduct({
        reference,
        reset: true,
        isLoginRefresh,
        isProductLoading: false,
      });
    }
    this.setState({
      reference: reference || {},
    });
  }

  async onPressDriver() {
    Keyboard.dismiss();
    if (!this.state.isLogin) {
      const res = await User.toLogin();
      if (!res) {
        return;
      }
      this.setState({ isLogin: res });
      this.queryProduct();
    }
    const { passengerList = [], passenger, passengerIDCard } = this.props;
    if (Utils.isCtripIsd()) {
      if (
        (!lodashIsEmpty(passenger) && passengerIDCard?.certificateType) ||
        (passengerList.length && lodashIsEmpty(passenger))
      ) {
        // 选中了驾驶员&&选中的驾驶员证件可用 || 账户内的驾驶员都不可用
        this.goToDriverList();
        return;
      }
      const isAdd = !passengerIDCard?.certificateType;
      this.goToDriverEdit(isAdd);
      return;
    }
    if (passengerList.length) {
      this.goToDriverList();
      return;
    }
    this.goToDriverEdit(true);
  }

  goToDriverList() {
    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_常旅' });
    this.push(Channel.getPageId().DriverList.EN);
  }

  handlePresssMore = () => {
    Keyboard.dismiss();
    CarLog.LogCode({ name: '点击_填写页_驾驶员更多和新增' });
    this.push(Channel.getPageId().DriverList.EN);
  };

  goToDriverEdit(isAdd?: boolean) {
    const { passenger } = this.props;
    this.push(Channel.getPageId().DriverEdit.EN, {
      passenger,
      isAdd,
      fromurl: 'Booking',
    });
    CarLog.LogCode({ name: '点击_驾驶员列表页_添加驾驶员' });
  }

  onPressFlightDelayRules() {
    CarLog.LogCode({ name: '点击_填写页_航班延迟政策' });
    const { setFlightDelayRulesModalVisible } = this.props;
    Keyboard.dismiss();
    setFlightDelayRulesModalVisible(true);
  }

  onPressBar() {
    const { addOnCodes, curInsPackageId } = this.props;
    const fees = [];
    if (addOnCodes?.length) {
      const rentalGuaranteeV2: RentalGuaranteeV2Type =
        getRentalGuaranteeV2(curInsPackageId);
      const selectInsInfo = rentalGuaranteeV2?.packageDetailList?.filter(item =>
        addOnCodes.includes(item?.uniqueCode),
      );
      selectInsInfo?.forEach(item => fees.push(item.currentDailyPrice));
    }
    CarLog.LogCode({
      name: '点击_填写页_底部_费用明细',

      info: {
        carAgentInsuranceFee: fees?.join(','),
      },
    });
    if (Utils.isCtripIsd()) {
      this.setPriceDetailModal();
      return;
    }

    if (Utils.isCtripOsd()) {
      this.setOsdPriceDetailModal();
    }
  }

  onCheckBarCheck = check => {
    this.setState({
      personalInfoChecked: check,
    });
    this.personalInfoChecked = check;
  };

  onCheckBarPress = () => {
    this.setState({
      personalInfoAuthModalVisible: true,
    });
  };

  hidePersonalInfoAuthModal = () => {
    this.setState({
      personalInfoAuthModalVisible: false,
    });
  };

  onPressTandC() {
    Keyboard.dismiss();
    // #ifndef weapp alipay 小程序业务差异化：调整页面跳转方式
    this.push(Channel.getPageId().Policy.EN);
    // #endif
    AppContext.PageInstance.push(Channel.getPageId().Policy.EN);
  }

  onPressCtripDepositFee = () => {
    xRouter.navigateTo({
      url: 'https://pages.c-ctrip.com/cars/doc/CreditRentAggrementV3.doc',
    });
  };

  onPressSelfServiceInstruction = () => {
    xRouter.navigateTo({ url: Document.selfServiceInstruction });
  };

  onPressCoupon() {
    Keyboard.dismiss();
    if (!this.state.isLogin) {
      this.handleLogin();
      return;
    }
    CarLog.LogCode({ name: '点击_填写页_优惠券' });
    if (Utils.isCtripIsd()) {
      this.setState({ isShowCouponModal: true });
    } else {
      this.push('Coupon');
    }
  }

  getActivityLog = () => {
    const { activityDetail } = this.props;
    const { title = '' } = activityDetail || {};
    const { vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const activityId = [];
    if (activityDetail?.promotions?.length > 0) {
      activityDetail?.promotions?.forEach(item => {
        if (item?.labelCode) {
          activityId.push(item.labelCode);
        }
      });
    } else if (activityDetail?.promotion?.labelCode) {
      activityId.push(activityDetail?.promotion?.labelCode);
    }
    return {
      vendorCode,
      vendorId: bizVendorCode,
      activityId,
      activityName: title,
    };
  };

  onPressActivity = (code?: string) => {
    Keyboard.dismiss();
    const data = this.getActivityLog();
    CarLog.LogCode({
      name: '点击_填写页_活动模块',

      info: {
        ...data,
      },
    });
    if (code) {
      this.setState({
        currentActivityCode: code,
        activityNoteModalVisible: true,
      });
    }
  };

  onCloseActivityNoteModal = () => {
    this.setState({
      activityNoteModalVisible: false,
    });
  };

  onPressAdjustPrice = () => {
    Keyboard.dismiss();
    this.setState({
      adjustPriceNoteModalVisible: true,
    });
  };

  onCloseAdjustPriceNoteModal = () => {
    this.setState({
      adjustPriceNoteModalVisible: false,
    });
  };

  // 开启轮询
  openPriceTimer = (needFirstFresh?: boolean) => {
    const { productReq, showPriceConfirm, queryEquipmentInfo } = this.props;
    if (this.priceTimer) {
      this.priceTimer.clearPriceTimer?.();
    }
    this.priceTimer = new PriceTimer();
    this.createPiceTimerBySelf = true;
    this.priceTimer.setPriceTimer(
      productReq,
      showPriceConfirm, // 设置CarDialog展示
      this.props.retry,
      needFirstFresh,
      // 增加额外设备轮询，防止缓存失效
      params => Utils.isCtripOsd() && queryEquipmentInfo(params),
    );
  };

  handleAppBackground = () => {
    this.backgroundTime = Date.now();
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_app_state,
      info: {
        state: 'background',
      },
    });
  };

  handleAppActive = () => {
    const { isShowPriceConfirm } = this.props;
    const intervalTime = Date.now() - this.backgroundTime;
    if (
      !!this.backgroundTime &&
      intervalTime > Enquiry.INTERVAL_TIME &&
      !isShowPriceConfirm
    ) {
      this.openPriceTimer(true);
    }
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_app_state,
      info: {
        time: Date.now() - this.backgroundTime,
        state: 'active',
      },
    });
  };

  componentDidMount() {
    super.componentDidMount();
    if (!Utils.isCtripIsd()) {
      this.onPageReady(null);
      // #ifndef weapp alipay 小程序业务差异化：13609/getCountries未使用redux注释
      this.props.queryCountrysInfo();
      // #endif
    }
    if (isHarmony && Utils.isCtripIsd()) {
      this.queryProduct(false, true);
      this.initLogInfo();
    }
    const {
      noNeedRefresh,
      productRes,
      isEasyLife,
      isPriceLoading,
      isPriceFail,
      isRebook,
      ctripOrderId,
      fetchQueryCancelFeeRebook = Utils.noop,
      optionalContactMethods,
      changeLocalContactsData,
    } = this.props;
    if (optionalContactMethods?.length > 0) {
      changeLocalContactsData(optionalContactMethods);
    }

    const isSelected = !!lodashGet(productRes, 'isSelected');
    const gs = lodashGet(productRes, 'gs') || {};
    const { id = 0, title = '' } = gs;

    if (isRebook && !!ctripOrderId) {
      fetchQueryCancelFeeRebook({ orderId: ctripOrderId });
    }
    if (!noNeedRefresh && !Utils.isCtripIsd()) {
      // 重新查询但需要保留用户在产品详情页的勾选，儿童座椅，保险
      this.queryProduct(true);
    }

    if (!Utils.isCtripIsd()) {
      this.openPriceTimer();
    }

    CarLog.LogTrace({
      key: LogKey.c_car_trace_book_insurance_222017,
      info: {
        insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        insuranceId: AppContext.InsuranceRules.insuranceSelectedIds,
      },
    });

    if (Utils.isCtripIsd()) {
      // 货架信息订单填写页漏斗埋点
      CarLog.LogTrace({
        key: LogKey.vac_car_trace_product_goodsshelf,
        info: {
          gsId: id,
          gsName: title,
          isEasyLife,
          isOptimize: isSelected,
        },
      });
    }

    this.appStateActiveCallBack = DeviceEventEmitter.addListener(
      'AppEnterForeground',
      this.handleAppActive,
    );
    this.appStateBackgroundCallBack = DeviceEventEmitter.addListener(
      'AppEnterBackground',
      this.handleAppBackground,
    );

    setTimeout(async () => {
      const isLogin = await User.isLogin();
      this.setState({ isLogin });

      this.initForm();
    });
    // setTimeout(() => {
    //   this.setState({ isSnapShotRender: true });
    // }, 1000);

    if (!isPriceLoading) {
      LogBookQueryPrice(!isPriceFail);
    }
    if (Utils.isCtripOsd()) {
      EventHelper.addEventListener(EventName.closeProductExtraInfoModal, () => {
        this.setEnableDragBack();
      });
    }
    AppContext.setIsReachedBookingPage(true);
  }

  lazyComponentDidMount() {
    if (!Utils.isCtripIsd()) {
      this.setState({ isFinishRender: true });
    }
    if (Utils.isCtripOsd()) {
      // 境外打开埋点
      LogProductInfo();
    }
    if (Utils.isCtripIsd()) {
      this.initialConfirmedData();
    }
    this.registerEvents();
  }

  initialConfirmedData = () => {
    this.addTimer(
      setTimeout(async () => {
        const isShowed = await CarStorage.loadAsync(
          StorageKey.CAR_BOOKING_CONFIRM,
          true,
          true,
        );
        if (!isShowed) {
          this.isShowConfirmed = true;
        }
      }, 10 * 1000),
    );
  };

  showInsRemindPopFun = () => {
    this.props.setInsRemindPopIsShow(true);
  };

  handleMaskLoading = () => {
    Loading.hideMaskLoading();
  };

  handleInsConfirmCallback = () => {
    const status = lodashGet(this.insConfirmData, 'status');
    if (status === 0 || status === 2) {
      this.props.insConfirmCallBack({
        insConfirmData: this.insConfirmData,
        callbackFun: this.handleMaskLoading,
        continueCreateOrder: this.continueCreateOrder,
        logInsurancePageActiveTime: this.logInsurancePageActiveTime,
      });
      Loading.showMaskLoading({
        cancelable: false,
      });
    } else {
      // 从保代页面回退后的场景，也需要清空标记
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.hasGoToInsConfirm,
        false,
      );
      this.logInsurancePageActiveTime();
    }
  };

  addInsConfirmBackToBookEvent = () => {
    EventHelper.addEventListener(EventName.insConfirmBackToBook, data => {
      const status = lodashGet(data, 'status');
      const selectedInsuranceList =
        lodashGet(data, 'data.selectedInsuranceList') || [];
      const callbackInsuranceId = selectedInsuranceList.map(m => m.insuranceId);
      const token = lodashGet(data, 'token');
      this.insConfirmData = {
        insConfirmInsList: selectedInsuranceList,
        callbackInsuranceId,
        token,
        status,
      };
      this.handleInsConfirmCallback();

      CarLog.LogTrace({
        key: LogKey.c_car_trace_book_insurance_callback_222017,
        info: {
          callbackInsuranceId,
          insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        },
      });
      // remove事件
      Event.removeEventListener(EventName.insConfirmBackToBook);
    });
  };

  registerEvents() {
    const { fromPage } = this.props;
    EventHelper.addEventListener(EventName.orderBack2Home, () => {
      Page.getRegisteredPageList(res => {
        const index = res?.findIndex(
          item => item === Channel.getPageId().Order.EN,
        );
        // 存在且不是最后一个
        if (index > -1 && index < res?.length - 1) {
          Page.popToPage(Channel.getPageId().Order.EN);
          EventHelper.sendEvent(EventName.modifyOrder2Order, {});
        } else {
          if (fromPage === Channel.getPageId().RecommendVehicle.EN) {
            return;
          }
          if (AppContext.isHomeCombine) {
            // @ts-ignore
            Page.backToLast({ animated: false });
            return;
          }
          const isFromHome = !!getRegisterPageData(Channel.getPageId().Home.EN);
          const isFromList =
            !isFromHome && !!getRegisterPageData(Channel.getPageId().List.EN);
          if (!(isFromHome || isFromList)) {
            return;
          }
          this.pop(Channel.getPageId()[isFromHome ? 'Home' : 'List'].EN);
          if (isFromList) {
            this.props.setVendorListModalData({ visible: false });
          }
        }
      });
    });
  }

  removeEvents() {
    Event.removeEventListener(EventName.orderBack2Home);
    Event.removeEventListener(EventName.insConfirmBackToBook);
    this.appStateActiveCallBack?.remove?.();
    this.appStateBackgroundCallBack?.remove?.();
    Event.removeEventListener(EventName.closeProductExtraInfoModal);
  }

  initForm() {
    const { passenger } = this.props;
    if (passenger) {
      this.props.selectDriver(passenger);
    }
    setTimeout(() => {
      this.onPageExposure();
    }, 100);

    if (Utils.isCtripOsd()) {
      this.getDriverInfoFromStorage();
    }
  }

  getDriverInfoFromStorage = () => {
    const { changeFormData } = this.props;
    // 声明需要从缓存获取的表单类型
    const storageSelectTypes = [IInputType.email, IInputType.localContacts]; // 本次只需要邮箱
    // 获取表单缓存信息
    CarStorage.load(StorageKey.DRIVER, true).then(result => {
      if (result) {
        try {
          const res = JSON.parse(result);
          if (!Array.isArray(res)) return;
          const dataDriver = res.reduce((m, v) => {
            if (v?.value && storageSelectTypes.includes(v?.type)) {
              return [...m, v];
            }
            return m;
          }, []);
          changeFormData(dataDriver);
        } catch (error) {
          CarLog.LogError(ErrorKey.e_book_get_storage_driver_info, error);
        }
      }
    });
  };

  componentWillUnmount() {
    super.componentWillUnmount();
    const {
      isRebook,
      ctripOrderId,
      resetCancelFeeRebook = Utils.noop,
    } = this.props;
    if (isRebook && !!ctripOrderId) {
      resetCancelFeeRebook();
    }
    this.props.clear();
    this.props.setSelectedLoanPayStageCount('');
    if (this.createPiceTimerBySelf) {
      // 如果询价轮询是booking页面创建的，说明没有经过产详页(比如h5保代页面跳转到填写页)
      this.priceTimer.clearPriceTimer();
    }
    this.removeEvents();
  }

  pageDidAppear() {
    super.pageDidAppear();
    const insuranceAgentToken = lodashGet(this.insConfirmData, 'token');
    if (!insuranceAgentToken) {
      // 从保代页面侧滑回退后的场景，也需要清空标记
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.hasGoToInsConfirm,
        false,
      );
    }
    this.logInsurancePageBackTime();
    this.refreshPrice();
  }

  refreshPrice = () => {
    const { orderId, queryPriceInfo } = this.props;
    if (orderId && this.needRefreshPrice) {
      this.needRefreshPrice = false;
      queryPriceInfo();
      // 小程序业务差异-redux 注释-sesame
      // ensureFunctionCall(initSesameAuthState, this);
    }
  };

  pageDidDisappear() {
    super.pageDidDisappear();
    Loading.hideMaskLoading();
    const {
      createOrderFailModalVisible,
      uniqueOrderModalVisible,
      flightErrorModalVisible,
    } = this.props;
    if (
      createOrderFailModalVisible ||
      uniqueOrderModalVisible ||
      flightErrorModalVisible
    ) {
      this.props.changeModalStatus({
        createOrderFailModalVisible: false,
        uniqueOrderModalVisible: false,
        flightErrorModalVisible: false,
      });
    }
    CarStorage.save(StorageKey.DRIVER, this.props.driverInfo, undefined, true);
  }

  onPageExposure() {
    const { payParams } = this.props;
    if (payParams && payParams.driver) {
      CarLog.LogTrace({
        key: LogKey.c_car_exposure_write_driver_recommend,
        info: {
          recommendDriverName: payParams.driver.name || '',
          recommendDriverCardNum: payParams.driver.idnumber || '',
          recommendDriverTelNum: payParams.driver.cellPhone || '',
        },
      });
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (
      Utils.isCtripIsd() &&
      this.props.isProductLoading &&
      !nextProps.isProductLoading
    ) {
      this.performanceMonitor.receiveResStart = new Date();
    }
    // 海外详情页优化新版，驾驶员国家发生变化时，查询驾照政策
  }

  setFirstScreenFinishRender() {
    if (!this.state.isFinishRender) {
      setTimeout(() => {
        this.setState({
          isFinishRender: true,
        });
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { isProductLoading, isFail } = this.props;
    if (
      Utils.isCtripIsd() &&
      prevProps.isProductLoading &&
      !isProductLoading &&
      !isFail
    ) {
      this.setFirstScreenFinishRender();
      this.onPageReady(getProductMapWithHandleCache());
    }

    if (!prevProps.isMaskLoading && this.props.isMaskLoading) {
      Loading.showMaskLoading({
        cancelable: false,
      });
    } else if (prevProps.isMaskLoading && !this.props.isMaskLoading) {
      Loading.hideMaskLoading();
    }

    // if (!prevProps.isOnMask && this.props.isOnMask) {
    //   // 开始截图
    //   setTimeout(() => {
    //     this.getSnapImageData();
    //   }, 50);
    // }

    if (!prevProps.orderId && this.props.orderId) {
      this.handleOrderSuccess();
    }

    // if (
    //   (!prevProps.orderId || !prevState.getSnapImageFinish) &&
    //   this.props.orderId &&
    //   this.state.getSnapImageFinish
    // ) {
    //   // 上传交易快照
    //   this.createSnapShotData();
    // }

    if (!prevProps.preLicensData && this.props.preLicensData) {
      this.goToRapidPayment();
    }

    // monitor sesame state
    this.onSesameAuthStatusChange();

    if (
      prevProps.ehiFreeDepositModalVisible !==
      this.props.ehiFreeDepositModalVisible
    ) {
      this.setEnableDragBack(!this.props.ehiFreeDepositModalVisible);
    }

    // 新版填写页的轮询需在productInfo接口回来后
    if (
      Utils.isCtripIsd() &&
      prevProps.isPriceTimerLoading &&
      !this.props.isPriceTimerLoading
    ) {
      this.openPriceTimer();
    }
  }

  onSesameAuthStatusChange = async () => {
    if (this.authenStatusTicket !== this.props.authenStatusTicket) {
      this.authenStatusTicket = this.props.authenStatusTicket;
      setTimeout(() => this.props.queryPriceInfo(), 500);
    }
  };

  onPressFormQuestion = () => {
    this.push('DriverIntroduction');
    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_详情按钮' });
  };

  onAddInstructFormPress = () => {
    Keyboard.dismiss();
    CarLog.LogCode({ name: '点击_填写页_额外驾驶员说明_按钮' });
    this.setState({
      addInstructModalVisible: true,
    });
  };

  showMiniDepositModal = () => {
    this.setState({
      miniDepositModalVisible: true,
    });
  };

  showZhimaModal = () => {
    this.setState({
      zhimaModalVisible: true,
    });
  };

  showApplyPenaltyInputModal = () => {
    this.setState({
      isShowApplyPenaltyInputModal: true,
    });
  };

  hideApplyPenaltyInputModal = () => {
    this.setState({
      isShowApplyPenaltyInputModal: false,
    });
  };

  getDataFail() {
    BbkToast.show(texts.orderFailTip);
  }

  showApproveExplainModal = () => {
    this.setState({
      approveExplainModalVisible: true,
      isApproveExplainLoading: true,
    });

    CarFetch.reservationTerms({
      termsType: 'PersonalInfoAuth',
    })
      .then(res => {
        if (res?.baseResponse?.isSuccess) {
          this.setState({
            approveExplain: res,
            isApproveExplainLoading: false,
          });
        } else {
          this.getDataFail();
        }
      })
      .catch(() => {
        this.getDataFail();
      });
  };

  hideApproveExplainModal = () => {
    this.setState({
      approveExplainModalVisible: false,
    });
  };

  ctripRentNeedValidateOrder = () => {
    const { ctripRentNeedValidateOrder } = this.props;
    return ctripRentNeedValidateOrder;
  };

  needValidateOrder = () => {
    const { needValidateOrder } = this.props;
    return needValidateOrder;
  };

  isPayAtStore = () => {
    const { showPayMode, depositPayType } = this.props;
    return (
      showPayMode === ShowPayModeType.Store &&
      depositPayType === DepositPayType.Store
    );
  };

  disableBooking = () => {
    this.disableBookingFlag = true;
    this.addTimer(
      setTimeout(() => {
        this.disableBookingFlag = false;
      }, 2000),
    );
  };

  resetRebookParams = () => {
    const { setRebookParamsOsd, isRebookOsd } = this.props;
    if (isRebookOsd) {
      setRebookParamsOsd(null);
    }
  };

  async handleOrderSuccess() {
    const { orderData, payParams } = this.props;
    // @ts-ignore
    const { orderId, payAmount, payToken, payLink } = orderData;

    if (
      orderId &&
      (payAmount || payToken || payLink) &&
      !(this.needValidateOrder() || this.isPayAtStore())
    ) {
      try {
        let payRes = null;
        Loading.showMaskLoading({
          cancelable: false,
        });
        payRes = await MiddlePay({
          params: payParams,
          scene: PayScene.BookingCreateOrder,
        });
        this.disableBooking();
        Loading.hideMaskLoading();
        const { success, showError } = payRes;
        LogPaymentCallback(success);
        if (success) {
          // 支付成功 清空列表页缓存
          AppContext.setUserFetchCacheId({
            actionType: 'bookingPaySuccess',
          });
          this.goToOrder();
        } else if (showError) {
          this.payErrorHandler();
        } else {
          this.goToOrder();
        }
        // 修改订单清除缓存
        this.resetRebookParams();
        return;
      } catch (e) {
        this.payErrorHandler();
      }
    }
    this.goToOrder();
  }

  payErrorHandler = () => {
    BbkToast.show('支付失败', 1, () => {
      this.goToOrder();
    });
  };

  async goToRapidPayment() {
    const {
      preLicensData,
      isOnlyCreditCard,
      payRequestId,
      payAmount,
      payParams,
      showPayMode,
      resetLoading,
      isSecretBox,
    } = this.props;
    const params = {
      ...payParams,
      amount: payAmount,
      isOnlyCreditCard,
      title: preLicensData && preLicensData.payTip,
      payRequestId,
      payType:
        showPayMode === ShowPayModeType.Auth
          ? PayType.RegularPayAndCredit
          : PayType.CtripCredit,
      busType: this.ctripRentNeedValidateOrder()
        ? PlatformRouter.BUS_TYPE.ISD_CREDIT
        : PlatformRouter.BUS_TYPE.ISD_AUTH,
    };
    try {
      const payRes = await MiddlePay({
        params,
        scene: PayScene.BookingCreditRentAuth,
      });
      // eslint-disable-next-line no-shadow
      const { success, payRequestId: resInfoPayRequestId, isNew } = payRes;
      LogPaymentCallback(success);
      if (success) {
        const data = {
          payRequestId: isNew ? resInfoPayRequestId : payRequestId,
          isSecretBox,
        };
        this.props.createOrder(data);
      } else {
        BbkToast.show(texts.payFail);
      }
    } catch (e) {
      BbkToast.show(texts.payFail);
    } finally {
      // 解决重复下单优惠券失效问题，createOrder 无法唤起 Loading
      // 由于 MiddlePay 内部会关闭 MaskLoading，支付返回统一设置 Loading 关闭，保证 Loading 状态统一
      resetLoading(false);
    }
  }

  getOrderPath = (oid?: number) => {
    let orderPath = '';
    const orderId = oid || this.props.orderId;
    const { ORDERDETAIL: url } = PlatformRouter.CAR_CROSS_URL;
    const disableNativeDragBackSuffix = '&dragBack=false';
    if (Utils.isCtripIsd()) {
      orderPath = url.NEWISD;
    } else if (Utils.isCtripOsd()) {
      orderPath = `${url.NEWOSD}`;
    }
    orderPath = `${orderPath}&orderId=${orderId}&from=${PlatformRouter.ORDER_BACK_PARAMS.newBook}${disableNativeDragBackSuffix}`;
    return orderPath;
  };

  getOrderDetailUrl = orderId => {
    const subEnvParam = __global.subEnv ? `&subEnv=${__global.subEnv}` : '';
    const domain = QUNAR_H5_DOMAIN[__global.env];
    const path = ORDER_DETAIL_PATH[process.env.TARO_ENV.toUpperCase()];
    return `${domain}${path}${subEnvParam}&orderId=${orderId}`;
  };

  goToOrder = (oid?: number) => {
    const orderId = oid || this.props.orderId;
    if (!orderId) return;
    CarLog.LogCode({ name: '点击_填写页_跳转新版订单详情页' });
    if (MiniChannel.isQunar()) {
      qunarUtils.openWebview({ url: this.getOrderDetailUrl(orderId) });
    } else {
      xRouter.navigateTo({ url: this.getOrderPath(oid) });
    }
    if (this.priceTimer) {
      this.priceTimer.clearPriceTimer();
    }
  };

  validateDepositPayment = () => {
    const { curDepositPayInfo } = this.props;
    return !lodashIsEmpty(curDepositPayInfo);
  };

  validate = () => {
    const { driverInfo, needFlightNo, isEasyLife } = this.props;
    const errors = driverInfo.filter(v => v.error);
    let isValid = !errors.length;
    let sequence = IBU_SEQUENCE;

    if (Utils.isCtripOsd()) {
      if (isEasyLife) {
        sequence = OSD_SEQUENCE;
      } else {
        sequence = OSD_NORMAL_SEQUENCE;
      }
    } else if (Utils.isCtripIsd()) {
      sequence = ISD_SEQUENCE;
    }
    if (isValid) {
      const noValues = driverInfo
        .filter(v => v.value === '' && !!sequence.find(s => s === v.type))
        .map(v => {
          if (v.type === 'flightNumber') {
            return { ...v, error: needFlightNo };
          }
          return { ...v, error: true };
        })
        .filter(v => v.error);
      if (noValues.length) {
        isValid = false;
        const noValues1 = noValues?.[0];
        logValidate(`${noValues1?.type} is empty or error`, noValues1?.value);
        this.props.changeFormData(noValues);
      }
    } else {
      const error1 = errors?.[0];
      logValidate(`${error1?.type} is error`, error1?.value);
    }

    return isValid;
  };

  async handleLogin() {
    const res = await User.toLogin();
    if (res) {
      this.setState({ isLogin: res });
      this.queryProduct();
    }
  }

  // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
  getSnapImageData = async () => {
    if (this.snapImageData) {
      return this.snapImageData;
    }
    const start = +new Date();
    const bookingData = await getBookingSnapImageData(this.scrollView);
    this.props.unMaskerDriver();
    const snapImageData = await getSnapImageData({
      materialModalRef: this.materialModalRef,
      priceDetailsRef: this.priceDetailsRef,
      insuranceSuitsModalRef: this.insuranceSuitsModalRef,
    });
    this.snapTime = +new Date() - start;
    this.snapImageData = [...snapImageData, bookingData];
    this.setState({
      getSnapImageFinish: true,
    });
    return this.snapImageData;
  };

  createSnapShotData = async () => {
    CarLog.LogCode({
      enName: ClickKey.C_BOOKING_ADD_SNAP_SHOT.KEY,
    });
    const { orderId } = this.props;
    const snaps = await this.getSnapImageData();
    const snapImageData = getSnapShotData({
      orderId,
      snaps,
    });
    this.props.saveSnapshot(snapImageData);
  };
  // #endif

  getCreateInsLoadingIsShow = () => this.props.createInsLoadingPopVisible;

  handleGoToInsConfirmPage = () => {
    const {
      priceVersion,
      insConfirmReqParam,
      setCreateInsLoadingIsShow,
      setCreateInsFailPopIsShow,
    } = this.props;
    InsuranceConfirmUtil.goToInsConfirmPage({
      eventName: EventName.insConfirmBackToBook,
      reqParams: insConfirmReqParam,
      setCreateInsLoadingIsShow,
      setCreateInsFailPopIsShow,
      getCreateInsLoadingIsShow: this.getCreateInsLoadingIsShow,
      callbackFun: () => {
        // 注册事件
        this.addInsConfirmBackToBookEvent();
        ProductReqAndResData.setData(
          ProductReqAndResData.keyList.hasGoToInsConfirm,
          true,
        );
        ProductReqAndResData.setData(
          ProductReqAndResData.keyList.priceVersionBeforeInsConfirm,
          priceVersion,
        );
      },
    });
    this.isForwardInsPage = true;
    this.insActiveTime = new Date().getTime();
  };

  continueCreateOrder = (
    inverseInsuranceIds?: Array<string | number>,
    insuranceAgentToken?: string,
  ) => {
    // 校验送车上门是否需要降级
    if (this.props.needDownGrade) {
      this.props.validateIsDownGrade({
        callbackFun: () => {
          this.continueCreateOrderAfterValidate(
            inverseInsuranceIds,
            insuranceAgentToken,
          );
        },
        // 请求失败后是否要继续下一步操作,如点击下一步失败，则可以直接进入到填写页，若是点击提交订单时获取降级信息失败，则需Toast提示用户
        isFailToContinue: false,
      });
      return;
    }
    this.continueCreateOrderAfterValidate(
      inverseInsuranceIds,
      insuranceAgentToken,
    );
  };

  continueCreateOrderAfterValidate = (
    inverseInsuranceIds?: Array<string | number>,
    insuranceAgentToken?: string,
  ) => {
    Loading.showMaskLoading({
      cancelable: false,
    });
    this.props.maskerDriver();
    this.props.createOrder({
      isCheckOrder: this.needValidateOrder(),
      inverseInsuranceIds,
      insuranceAgentToken,
      isSecretBox: this.props.isSecretBox,
      isContractTemplates: Utils.isCtripOsd(),
    });
    // #ifndef weapp alipay 小程序业务差异化：18631/AddSnapShot未使用redux注释
    // 重置交易快照状态
    this.setState({
      getSnapImageFinish: false,
    });
    this.snapImageData = null;
    // #endif
  };

  handleValidateScroll() {
    const { driverInfo } = this.props;
    if (this.scrollView && this.scrollView.current) {
      this.scrollView.current.scrollToPosition(0, this.dirverY, true);
    }
    CarLog.LogCode({
      name: '点击_填写页_表单验证',

      data: driverInfo,
    });
  }

  validateOsdDriverInfo = () => {
    const { passenger } = this.props;
    // 校验passenger中的firstName和lastName是否是全英文
    const reg = /^[a-z A-Z]+$/;
    const isPassengerNameValid =
      reg.test(passenger?.firstName || '') &&
      reg.test(passenger?.lastName || '');
    if (!isPassengerNameValid) {
      xShowToast({
        title: '输入英文姓名，如 WANG XIAOBAO',
        duration: 3000,
      });
      return false;
    }
    return true;
  };

  handleBookPress = async () => {
    const { isLogin } = this.state;
    const {
      passenger,
      isMaskLoading,
      isPriceLoading,
      curDepositPayInfo,
      showPayMode,
      payMode,
      selectedInsuranceId,
      bookPriceTrackInfo,
      depositPayType,
      driversMap,
      oldAge,
      yongAge,
      selectedLoanPayStageCount,
      setPassengerError,
      passengerList,
      config: remoteQConfig,
      checkFlightNoLoading,
      isRefactor,
      isEasyLife2024,
      // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
      isTecentAds,
      tecentAdsClickedStatus,
      setTecentAdsClickedStatus,
      // #endif
    } = this.props;
    const isNoResultNew = CarServerABTesting.isNoResult();

    if (this.disableBookingFlag) {
      return;
    }

    // 在航班号校验期间不可提交
    if (checkFlightNoLoading) {
      return;
    }

    // loading时不可提交
    // fix bug: http://iwork.ctripcorp.com/#/carddetail/2567/4803/9149/552202
    if (isPriceLoading) {
      return;
    }

    // 防止多次点击提交订单
    if (isMaskLoading) {
      return;
    }

    if (!isLogin) {
      this.handleLogin();
      return;
    }

    if (Utils.isCtripIsd()) {
      if (lodashIsEmpty(passenger)) {
        setPassengerError(true);
        if (passengerList?.length) {
          xShowToast({ title: texts.emptyDriver, duration: 3000 }); // 请选择驾驶员
        } else {
          xShowToast({ title: texts.drivers_addDriver, duration: 3000 }); // 请新增驾驶员
        }
        this.handleValidateScroll();
        return;
      }
      if (!ValidatePassenger(driversMap, oldAge, yongAge, true)) {
        this.handleValidateScroll();
        return;
      }
    }

    if (Utils.isCtripOsd()) {
      if (!this.validateOsdDriverInfo()) {
        this.handleValidateScroll();
        return;
      }
    }

    if (!this.validate()) {
      this.handleValidateScroll();
      return;
    }

    const isValid = this.validateDepositPayment();

    // 出境免押
    if (Utils.isCtripOsd() && !isValid) {
      this?.scrollView?.current?.scrollToPosition(0, this.osdDepositPayY, true);
      xShowToast({ title: texts.depositPaymentTip, duration: 3000 });
      return;
    }

    // 程信分押金支付方式校验
    if (CarABTesting.isCreditRent() && !isValid) {
      if (this.scrollView && this.scrollView.current) {
        this.scrollView.current.scrollToPosition(0, this.depositPayY, true);
      }
      xShowToast({ title: texts.depositPaymentTip, duration: 3000 });
      CarLog.LogCode({
        name: '点击_填写页_押金支付方式验证',

        data: curDepositPayInfo,
      });
      return;
    }

    // @zxy 去掉enName中的状态
    // traceCode 用于关联 点击提交订单和支付回调埋点
    AppContext.setBookingTraceCode();
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.createOrderClickId,
      uuid(),
    );
    CarLog.LogCode({
      name: '点击_填写页_底部_去支付',
      showPayMode,
      payMode,
      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
      insuranceId: selectedInsuranceId,
      traceCode: AppContext.Booking.traceCode,
      isRefactor: isRefactor ? '1' : '',
      data: {
        ...bookPriceTrackInfo,
        depositPayType,
        createOrderClickId: ProductSelectors.getCreateOrderClickId(),
        isContainInstalments: !!selectedLoanPayStageCount,
      },
      iseasyLife2024: isEasyLife2024,
      info: {
        isRecommendOrder: isNoResultNew ? '1' : '0',
        requestId: getRequestId(),
        newRecommendType: this.props.newRecommendType,
      },
    });

    // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
    if (isTecentAds && !tecentAdsClickedStatus?.booking) {
      CarLog.LogCode({
        key: LogKey.c_rn_car_trace_click,
        name: '广告归因_下单_点击',
        pageId: this.getPageId(),
        sourceFrom: AppContext.CarEnv.appType,
        distributionChannelId: AppContext.MarketInfo.childChannelId,
        sid: AppContext.MarketInfo.sId,
        aid: AppContext.MarketInfo.aId,
        info: {
          // 腾讯广告标识
          adIndentifier: 'gggy',
          isTecentAds: true,
        },
      });

      // 设置填写页已点击标识，避免重复触发埋点
      setTecentAdsClickedStatus?.('booking', true);
    }
    // #endif

    this.needRefreshPrice = true;

    // 关闭费用明细弹层
    if (Utils.isCtripIsd() && this.state.priceDetailModalVisible) {
      this.closePriceDetailModal();
    }
    // 关闭海外费用明细
    if (Utils.isCtripOsd() && this.state.osdPriceDetailModalVisible) {
      this.closeOsdPriceDetailModal();
    }
    // 展示个人信息授权勾选弹窗
    const showPersonalInfoCheckModal =
      !this.personalInfoChecked && remoteQConfig?.personalInfoAuthCheck;
    // 检测是否选购了自营险
    if (this.selectInsuranceStatus()) {
      if (showPersonalInfoCheckModal) {
        this.onCheckBarPress();
      } else {
        this.handleGoToInsConfirmPage();
      }
    } else if (showPersonalInfoCheckModal) {
      this.onCheckBarPress();
    } else {
      this.continueCreateOrder();
    }
  };

  checkedAndToBook = () => {
    this.onCheckBarCheck(true);
    this.hidePersonalInfoAuthModal();
    this.handleBookPress();
  };

  selectInsuranceStatus = () => {
    const { selectedInsuranceId, config: remoteQConfig } = this.props;
    // 检测是否选购了自营险
    return (
      (Utils.isCtripIsd() &&
        lodashGet(selectedInsuranceId, 'length') > 0 &&
        remoteQConfig?.insuranceFlag) ||
      (Utils.isCtripOsd() && lodashGet(selectedInsuranceId, 'length') > 0)
    );
  };

  onConfirmFightNo() {
    if (this.scrollView && this.scrollView.current) {
      this.scrollView.current.scrollToPosition(0, this.dirverY + 120, true);
    }
  }

  onCancelFightNo = () => {
    // TODO push to list to filter
    this.push('List');
  };

  onPressCouponModalButton = type => {
    const { setCouponPreValidationModalVisible, queryPriceInfo } = this.props;
    const imUrl = getImAddress({
      pageId: Channel.getPageId().Book.ID,
      isPreSale: 1,
    });
    switch (type) {
      case ButtonAction.scrollToCoupon:
        setCouponPreValidationModalVisible(false, null);
        queryPriceInfo();
        if (this.scrollView && this.scrollView.current) {
          // 优惠券模块滚动到屏幕正中间位置计算（优惠券模块置顶滚动距离 - （屏幕一半高度 - 优惠券模块一半高度）- 头部修正高度）
          const couponY =
            this.couponY +
            this.dirverY -
            (vh(50) -
              this.couponHeight / 2 -
              (DEFAULT_HEADER_HEIGHT + fixOffsetTop()));
          this.scrollView.current.scrollToPosition(0, couponY, true);
        }
        break;
      case ButtonAction.goToIM:
        setCouponPreValidationModalVisible(false, null);
        xRouter.navigateTo({ url: imUrl });
        break;
      case ButtonAction.closeModal:
      default:
        setCouponPreValidationModalVisible(false, null);
        break;
    }
  };

  onLayoutCoupon = e => {
    this.couponY = e.nativeEvent.layout.y;
    this.couponHeight = e.nativeEvent.layout.height;
  };

  onLayoutDriver(e) {
    this.dirverY = e.nativeEvent.layout.y;
  }

  onLayoutDepositPay = e => {
    this.depositPayY = e.nativeEvent.layout.y;
  };

  onLayoutOsdDepositPay = e => {
    const layoutY = e?.nativeEvent?.layout?.y;
    if (layoutY) {
      this.osdDepositPayY = layoutY + 260;
    }
  };

  getDepositTestId = () => {
    const { bookPriceTrackInfo } = this.props;
    return CarLog.createExposureId(
      LogKey.c_car_trace_book_deposit_payment_exposure_222017,
      {
        bookPriceTrackInfo,
      },
    );
  };

  getCouponTestId = data =>
    CarLog.createExposureId(LogKey.c_car_exposure_write_coupon, data);

  getActivityTestId = data =>
    CarLog.createExposureId(LogKey.c_car_exposure_write_activity, data);

  jumpToGuidePage = (guideTabId?: number) => {
    const { productRentalLocationInfo } = this.props;
    const param = getGuidePageParam(
      guideTabId,
      Utils.isCtripIsd(),
      productRentalLocationInfo,
    );
    AppContext.PageInstance.push(Channel.getPageId().Guide.EN, {
      pageParam: param,
    });
  };

  gotoGuidePage = guideTabId => {
    Keyboard.dismiss();
    if (Utils.isCtripIsd()) {
      CarLog.LogCode({ name: '点击_填写页_取还车地图及指引' });
    }
    this.jumpToGuidePage(guideTabId);
  };

  goBack = () => {
    const { hasBookingConfirmInfo } = this.props;
    CarLog.LogCode({ name: '点击_填写页_返回上级页面' });
    if (Utils.isCtripIsd() && this.isShowConfirmed && hasBookingConfirmInfo) {
      this.setState({ showConfirm: true });
      this.isShowConfirmed = false;
      CarStorage.save(StorageKey.CAR_BOOKING_CONFIRM, '1', '24H', true);
      return;
    }
    this.pop();
  };

  clickSideToolBoxBtn = () => {
    CarLog.LogCode({ name: '点击_填写页_右上角侧边栏按钮点击' });
  };

  onBackAndroid = () => {
    const {
      supplierModalVisible,
      depositIntroduceModalVisible,
      easyLifePopVisible,
      depositRateDescriptionModalVisible,
      setDepositRateDescriptionModalVisible,
      ehiFreeDepositModalVisible,
      setEhiFreeDepositModalVisible,
    } = this.props;
    const {
      priceDetailModalVisible,
      osdPriceDetailModalVisible,
      productConfirmModalVisible,
      driverLicenseModalVisible,
      optimizationStrengthenModalVisible,
      carServiceDetailVisible,
      serviceClaimMoreVisible,
      isShowOptimizeStoreModal,
      personalInfoAuthModalVisible,
      showConfirm,
      addInstructModalVisible,
      miniDepositModalVisible,
      zhimaModalVisible,
      isShowCouponModal,
      activityNoteModalVisible,
      approveExplainModalVisible,
    } = this.state;
    if (isShowOptimizeStoreModal) {
      this.hideOptimizeStoreModal();
    } else if (supplierModalVisible) {
      this.props.changeModalStatus({
        supplierModalVisible: false,
      });
    } else if (depositIntroduceModalVisible) {
      this.props.changeModalStatus({
        depositIntroduceModalVisible: false,
      });
    } else if (priceDetailModalVisible) {
      this.closePriceDetailModal();
    } else if (osdPriceDetailModalVisible) {
      this.closeOsdPriceDetailModal();
    } else if (easyLifePopVisible) {
      this.props.onPressEasyLife(false);
    } else if (productConfirmModalVisible) {
      this.closeProductConfirmModal();
    } else if (driverLicenseModalVisible) {
      this.closeDriverLicenseModal();
    } else if (carServiceDetailVisible) {
      this.hideCarServiceDetail();
    } else if (serviceClaimMoreVisible) {
      this.hideServiceClaimMore();
    } else if (optimizationStrengthenModalVisible) {
      this.closeOptimizationStrengthenModal();
    } else if (personalInfoAuthModalVisible) {
      this.hidePersonalInfoAuthModal();
    } else if (depositRateDescriptionModalVisible) {
      setDepositRateDescriptionModalVisible(false);
    } else if (showConfirm) {
      this.closeBookingConfirmModal();
    } else if (addInstructModalVisible) {
      // 关闭增加多名驾驶员弹层
      this.onCloseSelected();
    } else if (miniDepositModalVisible) {
      // 关闭冻结押金弹层
      this.closeMiniDepositModal();
    } else if (zhimaModalVisible) {
      // 关闭芝麻免押流程弹层
      this.closeZhimaModal();
    } else if (isShowCouponModal) {
      // 关闭优惠券弹层
      this.closeCouponModal();
    } else if (activityNoteModalVisible) {
      // 关闭活动说明弹层
      this.onCloseActivityNoteModal();
    } else if (ehiFreeDepositModalVisible) {
      // 关闭减免规则弹窗
      setEhiFreeDepositModalVisible(false);
    } else if (approveExplainModalVisible) {
      // 关闭个人身份信息授权声明弹层
      this.hideApproveExplainModal();
    } else {
      this.goBack();
    }
  };

  closePriceChangePop = () => {
    this.props.setPriceChangePopIsShow(false);
  };

  // 处理从保代回来后的变价弹层左侧按钮
  handlePriceChangeLeftButton = () => {
    this.closePriceChangePop();
    this.props.refreshList();
    AppContext.PageInstance.pop(Channel.getPageId().List.EN);

    const callbackInsuranceId =
      lodashGet(this.insConfirmData, 'callbackInsuranceId') || [];
    const { showPayMode, payMode, priceChangeCode } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_变价弹层_重新选车',
      priceChangeType: priceChangeCode,
      payMode,
      showPayMode,
      insuranceId: callbackInsuranceId,

      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
    });
  };

  // 处理从保代回来后的变价弹层右侧按钮
  handlePriceChangeRightButton = () => {
    this.closePriceChangePop();
    /* eslint-disable max-len */
    const preSelectedIns =
      ProductReqAndResData.getData(
        ProductReqAndResData.keyList.priceChangeBeforeIns,
      ) || [];
    const insConfirmInsList =
      lodashGet(this.insConfirmData, 'insConfirmInsList') || [];
    const insuranceAgentToken = lodashGet(this.insConfirmData, 'token');
    const callbackInsuranceId =
      lodashGet(this.insConfirmData, 'callbackInsuranceId') || [];
    const inverseInsuranceIds = InsuranceConfirmUtil.getInverseInsuranceIds(
      preSelectedIns,
      insConfirmInsList,
    );

    const { showPayMode, payMode, priceChangeCode } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_变价弹层_去支付',
      priceChangeType: priceChangeCode,
      payMode,
      showPayMode,
      insuranceId: callbackInsuranceId,

      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
    });
    this.continueCreateOrder(inverseInsuranceIds, insuranceAgentToken);
  };

  // 处理保险订单创建失败弹层左侧按钮
  handleCreateInsFailPopLeftButton = () => {
    this.props.setCreateInsFailPopIsShow(false);
    setTimeout(() => {
      this.handleGoToInsConfirmPage();
    }, 1000);
  };

  // 处理保险订单创建失败弹层右侧按钮
  handleCreateInsFailPopRightButton = () => {
    this.props.setCreateInsFailPopIsShow(false);
    this.props.setCreateInsLoadingIsShow(false);
    // @ts-ignore
    this.insConfirmData = {
      status: 2,
    };
    this.handleInsConfirmCallback();
  };

  closeBookingConfirmModal = () => {
    this.setState({ showConfirm: false });
  };

  closeBookingConfirmModalWithPop = () => {
    this.setState({ showConfirm: false });
    this.pop();
  };

  // 从保代回来后的变价弹层曝光
  onPriceChangeExposure = () => {
    CarLog.LogTrace({
      key: LogKey.c_car_trace_book_insurance_changeprice_222017,
      info: {
        priceChangeType: this.props.priceChangeCode,
        payMode: this.props.payMode,
        showPayMode: this.props.showPayMode,
        insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
      },
    });
  };

  // 保代页面回退时间
  logInsurancePageBackTime = () => {
    if (this.isForwardInsPage) {
      this.insuranceEndTime = new Date().getTime();
    }
  };

  // 保代页面停留时长
  logInsurancePageActiveTime = () => {
    if (this.isForwardInsPage) {
      this.insuranceEndTime = new Date().getTime();

      CarLog.LogTrace({
        key: LogKey.c_car_trace_book_insurance_activetime_222017,
        info: {
          insuranceBeginTime: this.insActiveTime,
          insuranceEndTime: this.insuranceEndTime,
          payMode: this.props.payMode,
          showPayMode: this.props.showPayMode,
          insuranceActiveTime: this.insuranceEndTime - this.insActiveTime,
          insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        },
      });
      this.isForwardInsPage = false;
      this.insActiveTime = 0;
      this.insuranceEndTime = 0;
    }
  };

  // 关闭额外驾驶员说明模态框
  onCloseSelected = () => {
    this.setState({
      addInstructModalVisible: false,
    });
  };

  closeMiniDepositModal = () => {
    this.setState({
      miniDepositModalVisible: false,
    });
  };

  closeZhimaModal = () => {
    this.setState({
      zhimaModalVisible: false,
    });
  };

  gotoExtras = () => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品',

      modalVisible: true,
    });
    this.push(Channel.getPageId().Extras.EN);
  };

  getOrderListUrlHost = () => {
    const { env } = Application;
    switch (env) {
      case 'dev':
      case 'fat':
        return 'http://order.car.fat4556.qa.nt.ctripcorp.com';
      case 'uat':
        return 'http://order.car.uat.qa.nt.ctripcorp.com';
      case 'prod':
      default:
        return 'http://order.car.ctripcorp.com';
    }
  };

  // #ifndef mini
  getModifyOrderModalData = () => {
    // @ts-ignore
    switch (AppContext.fromType) {
      case '1': // 修改订单的取消重订
        return {
          visible: true,
          title: modifyOrderModalText.submitSuccess.title,
          contentText: modifyOrderModalText.submitSuccess.content,
          titleHeightlightStyle: TitleHightlightType.Success,
        };
      default:
        return {};
    }
  };

  // #endif
  closePriceDetailModal = () => {
    this.setState({ priceDetailModalVisible: false });
  };

  setPriceDetailModal = () => {
    this.setState({
      priceDetailModalVisible: !this.state.priceDetailModalVisible,
    });
  };

  closeOsdPriceDetailModal = () => {
    this.setState({ osdPriceDetailModalVisible: false });
  };

  setOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: !this.state.osdPriceDetailModalVisible,
    });
  };

  showEasyLifeModal = () => {
    this.props.onPressEasyLife(true);
  };

  closeProductConfirmModal = () => {
    this.setState({ productConfirmModalVisible: false });
  };

  openProductConfirmModal = (productConfirmAnchor: LayoutPartEnum) => {
    const config = {
      [LayoutPartEnum.VehicleConfig]: '点击_门店信息',
      [LayoutPartEnum.Mileage]: '点击_限制政策',
      [LayoutPartEnum.VehicleDetail]: '点击_门店信息',
    };
    const type = config[productConfirmAnchor];
    if (type) {
      CarLog.LogCode({
        key: LogKey.CLICK_KEY,
        name: type,
        pageId: Channel.getPageId().Book.ID,
      });
    }
    this.setState({ productConfirmModalVisible: true, productConfirmAnchor });
  };

  closeOptimizationStrengthenModal = () => {
    this.setState({ optimizationStrengthenModalVisible: false });
  };

  handleEtcIntroModalClose = () => {
    const { setEtcIntroModal } = this.props;
    setEtcIntroModal(false);
  };

  openOptimizationStrengthenModal = () => {
    this.setState({ optimizationStrengthenModalVisible: true });
  };

  onPressLimit = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.Mileage);
  };

  onPressVendor = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.VehicleDetail);
  };

  onPickupDriveTimeCalculated = (time: number) => {
    AppContext.setDriveTime(time);
  };

  popToVendorList = (type?: PriceAlertType) => {
    const {
      fromPage,
      addSaleOutList,
      addRecommendSaleOutList,
      // #ifdef mini 小程序业务差异化：列表缩短预订流程
      refreshReducePathSoldOut,
      // #endif
    } = this.props;
    if (type === PriceAlertType.SoldOut) {
      const productKey = Utils.getProductKey(getProductRequestReference());
      if (fromPage === Channel.getPageId().RecommendVehicle.EN) {
        addRecommendSaleOutList(productKey);
      } else {
        addSaleOutList(productKey);
      }
      // #ifdef mini 小程序业务差异化：列表缩短预订流程
      if (fromPage === Channel.getPageId().List.EN) {
        refreshReducePathSoldOut();
      }
      // #endif
      // 盲盒售罄回到列表页强刷，因为是按价格顺排，不存在售罄。
      if (getProductRequestReference()?.secretBoxParam) {
        setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
      }
    }
    AppContext.PageInstance.pop(fromPage || Channel.getPageId().VendorList.EN);
  };

  // 各种异常状态点击处理
  priceAlertHandler = (type?: PriceAlertType) => {
    switch (type) {
      case PriceAlertType.PriceChange:
      case PriceAlertType.Error:
      case PriceAlertType.PriceCacheError:
        // 重试
        this.props.queryProduct({ reset: true, isStartPriceTimer: true });
        break;
      default:
        // 回退到新详情页
        this.popToVendorList(type);
    }
  };

  initListData = () => {
    const { firstScreenParam } = this.props;
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.bookingFirstScreenParam,
      firstScreenParam,
    );
  };

  initLogInfo = () => {
    const { setLogInfo, vendorPriceInfo } = this.props;
    if (setLogInfo) {
      setLogInfo({ allTags: vendorPriceInfo?.allTags });
    }
  };

  getActivityNoteModalProps = () => {
    const { activityDetail } = this.props;
    const { activityNoteModalVisible, currentActivityCode } = this.state;
    const currentPromotion = activityDetail?.promotions?.find(
      item => item.code === currentActivityCode,
    );
    const { title = '', description = '' } = currentPromotion || {}; // 处理描述内容，兼容有无换行符的情况
    const content = description
      ? description.includes('\n')
        ? description.split('\n').slice(1) // 有换行符时，移除第一行
        : [description] // 无换行符时，使用完整描述
      : [];
    return {
      visible: activityNoteModalVisible,
      onMaskPress: this.onCloseActivityNoteModal,
      style: styles.shadow,
      title: `${title}${texts.couponActivity}`,
      content,
      hasActivityBg: true,
      location: 'bottom',
    };
  };

  // 调整价格说明弹窗Props
  getAdjustPriceNoteModalProps = () => {
    const { adjustPriceDetail } = this.props;
    const { adjustPriceNoteModalVisible } = this.state;
    const { desc, shortDesc } = adjustPriceDetail || {};
    return {
      visible: adjustPriceNoteModalVisible,
      onMaskPress: this.onCloseAdjustPriceNoteModal,
      style: styles.shadow,
      title: shortDesc,
      content: [desc],
      hasActivityBg: true,
      location: 'bottom',
    };
  };

  showOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: true,
    });
  };

  hideOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: false,
    });
  };

  showServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: true,
    });
  };

  hideServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: false,
    });
  };

  showCarServiceDetail = code => {
    Keyboard.dismiss();
    this.setState({
      carServiceDetailVisible: true,
      showServiceDetailCode: code,
    });
  };

  hideCarServiceDetail = () => {
    this.setState({
      carServiceDetailVisible: false,
      showServiceDetailCode: '',
    });
  };

  closeCouponModal = () => {
    this.setState({
      isShowCouponModal: false,
    });
  };

  showDriverLicenseModal = () => {
    const { driversMap, passenger } = this.props;
    const { vehicleInfo, vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const { vehicleCode } = vehicleInfo || {};
    const driverLicenseValues = driversMap?.driverLicense?.split('|');
    CarLog.LogCode({
      name: '点击_填写页_驾照切换',

      info: {
        vendorCode,
        vendorId: bizVendorCode,
        vehicleCode,
        nationality: passenger?.nationality,
        driverLicenseCountry: driversMap?.driverLicenseName,
        driverLicenseType: driverLicenseValues?.[1],
        driverAge: driversMap?.age,
      },
    });
    this.setState({
      driverLicenseModalVisible: true,
    });
  };

  closeDriverLicenseModal = () => {
    this.setState({
      driverLicenseModalVisible: false,
    });
  };

  handleFuelDescClose = () => {
    const { setFuelDescriptionModalVisible } = this.props;
    setFuelDescriptionModalVisible(false);
  };

  hideLocalContactsModal = () => {
    this.setState({
      localContactsModalVisible: false,
    });
  };

  showLocalContactsModal = () => {
    Keyboard.dismiss();
    this.setState({
      localContactsModalVisible: true,
    });
    CarLog.LogCode({ name: '点击_填写页_其他联系方式_切换' });
  };

  localContactsInputFocus = isFocus => {
    this.setState({
      localContactsInputIsFocus: isFocus,
    });
  };

  onSelectContactType = item => {
    const {
      pickUpAreaCode,
      changeFormData,
      localContactsData,
      changeLocalContactsData,
    } = this.props;
    let value = '';
    let contactType = -1;
    if (!localContactsData?.length) return;
    const curLocalContactsData = localContactsData?.map(info => {
      const isSelected = item.contactType === info.contactType;
      if (isSelected) {
        value = info.data;
        contactType = info.contactType;
      }
      return {
        ...info,
        isSelected: item.contactType === info.contactType,
      };
    });
    if (contactType > -1) {
      CarLog.LogCode({
        name: '点击_填写页_其他联系方式_选择',

        info: {
          uidTypeSelected: contactType,
        },
      });
    }
    changeLocalContactsData(curLocalContactsData);
    changeFormData([
      {
        type: 'localContacts',
        contactType: item.contactType,
        contactTypeName:
          item.contactType === ContactType.localPhone
            ? `+${pickUpAreaCode}`
            : item.title,
        value,
      },
    ]);
  };

  getApproveExplainModalBtnFn = () => {
    const { personalInfoAuthModalVisible, personalInfoChecked } = this.state;
    if (personalInfoAuthModalVisible) {
      return this.checkedAndToBook;
    }
    if (personalInfoChecked) {
      return this.handleBookPress;
    }
    return this.onCheckBarCheck;
  };

  handleBusinessLicenseClose = () => {
    const { setBusinessLicenseModalVisible } = this.props;
    setBusinessLicenseModalVisible(false);
  };

  gotoExtrasV2 = () => {
    EventHelper.sendEvent(EventName.showProductExtraInfoModal, null);
    this.setEnableDragBack(false);
  };

  closeExtrasInfoModal = () => {
    this.setState({
      extrasInfoVisible: false,
    });
  };

  setAsMobile = asMobileNumber => {
    const { changeLocalContactsData, localContactsData, changeFormData } =
      this.props;
    let contactType = -1;
    const curLocalContactsData = localContactsData?.map(item => {
      if (item.isSelected) {
        // eslint-disable-next-line prefer-destructuring
        contactType = item.contactType;
      }
      return {
        ...item,
        data: item.isSelected ? asMobileNumber : item.data,
      };
    });
    if (contactType > -1) {
      CarLog.LogCode({
        name: '点击_填写页_其他联系方式_同手机号',

        info: {
          uidTypeSelected: contactType,
        },
      });
    }
    changeLocalContactsData(curLocalContactsData);
    changeFormData([
      {
        type: 'localContacts',
        value: asMobileNumber,
      },
    ]);
    Keyboard.dismiss();
  };

  getAsMobileNumber = () => {
    if (Utils.isCtripIsd() || !isIos) return '';
    const { driverInfo } = this.props;
    let areaCode = '';
    let mobile = '';
    let localContactValue = '';
    driverInfo.forEach(item => {
      if (item.type === IInputType.mobilePhone) {
        mobile = item.value;
      }
      if (item.type === IInputType.areaCode) {
        areaCode = item.value;
      }
      if (item.type === IInputType.localContacts) {
        localContactValue = item.value;
      }
    });
    const number = areaCode && mobile ? `+${areaCode}-${mobile}` : '';
    if (localContactValue === number) {
      return '';
    }
    return number;
  };

  getLogData = () => {
    const { productRes } = this.props;
    const { vendorInfo, vehicleInfo, productInfo } = productRes || {};
    return {
      pStoreCode: productInfo?.pickupStoreInfo?.storeCode,
      rStoreCode: productInfo?.returnStoreInfo?.storeCode,
      vehicleCode: vehicleInfo?.vehicleCode,
      vehicleName: vehicleInfo?.name,
      vendorCode: vendorInfo?.vendorCode,
      vendorName: vendorInfo?.vendorName,
    };
  };

  getFooterBarData = ({
    deductAmount,
    totalPrice,
    packagePrice,
    currencyCode,
  }) => {
    return {
      price: totalPrice, // 当前价
      originPrice: deductAmount ? packagePrice : 0, // 原始价
      discountPrice: deductAmount, // 折扣力度
      currencyCode, // 货币单位
    };
  };

  feeDetailLogData = () => {
    const { orderPriceInfo } = this.props;
    const { reference } = this.state;
    const footerBarData = this.getFooterBarData(orderPriceInfo ?? {});
    const logData = this.getLogData();
    if (reference && footerBarData?.price !== undefined) {
      return {
        orderAmount: footerBarData?.price,
        discountAmount: footerBarData?.discountPrice,
        ...logData,
      };
    }
    return null;
  };

  operationHandle = (index: number) => {
    const { zhimaFlag, sesameGoAuth } = this.props;
    if (zhimaFlag && zhimaFlag.indexOf('gozhima') >= 0) {
      const logData = this.feeDetailLogData();
      if (logData) {
        CarLog.LogCode({
          enName: 'C_BOOKING_SEASAMEAUTH_GOAUTH', // #43-45
          name: '点击_填写页_芝麻免押_立即验证',
          info: logData,
        });
      }
      if (zhimaFlag === 'gozhima0') {
        CarLog.LogCode({
          enName:
            index === 0 ? 'C_BOOKING_GO_ZHIMA' : 'C_BOOKING_DEPOSIT_GO_ZHIMA',
          name:
            index === 0
              ? '点击-填写页-芝麻立即验证_供应商'
              : '点击-填写页-芝麻立即验证_押金模块',
        });
      } else {
        CarLog.LogCode({
          enName:
            index === 0 ? 'C_BOOKING_TWO_ZHIMA' : 'C_BOOKING_DEPOSIT_TWO_ZHIMA',
          name:
            index === 0
              ? '点击-填写页-芝麻验证第二笔_供应商'
              : '点击-填写页-芝麻验证第二笔_押金模块',
        });
      }
      sesameGoAuth();
    }
  };

  toggleZhimaModal = flag => {
    this.setState({
      showZhimaModal: flag,
    });
  };

  // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
  getTecentAdsBuryData = () => {
    return {
      ubtKeyName: LogKey.EXPOSURE_EKY,
      data: {
        key: LogKey.EXPOSURE_EKY,
        name: '广告归因_访问填写页_曝光',
        pageId: this.getPageId(),
        sourceFrom: AppContext.CarEnv.appType,
        distributionChannelId: AppContext.MarketInfo.childChannelId,
        sid: AppContext.MarketInfo.sId,
        aid: AppContext.MarketInfo.aId,
        info: {
          // 腾讯广告标识
          adIndentifier: 'gggy',
          isTecentAds: true,
        },
      },
    };
  };
  // #endif

  renderPage() {
    const {
      positivePolicies,
      activityDetail,
      adjustPriceDetail,
      cancelRuleInfo,
      couponList,
      invoiceInfo,
      confirmInfo,
      payParams,
      selectedIdType,
      isFail,
      depositPayType,
      curInsPackageId,
      isShowPriceConfirm,
      membershipPerception,
      isProductLoading,
      needDownGrade,
      ptime,
      rtime,
      addOnCodes,
      isRebook,
      resCancelFeeRebook,
      changeSelectInsurance,
      ctripOrderId,
      rebookPenalty,
      setRebookPenalty,
      fromPage,
      isMergeDeposit,
      changeCoupon,
      isPriceLoading,
      toolBoxCustomerJumpUrl,
      isSecretBox,
      config: remoteQConfig,
      agreeSubmitName = texts.agreeAndBook,
      isKlb,
      changeFormData,
      currenctTotalPrice,
      driverLicenseItems,
      curDriverLicense,
      selectCurDriverLicense,
      passenger,
      isBusinessLicenseModalVisible,
      isRefactor,
      addInstructData,
      isEasyLife2024,
      hasExtrasProducts,
      isFuelDescriptionModalVisible,
      isRebookOsd,
      osdModifyOrderNote,
      pickUpAreaCode,
      logBaseInfo,
      isEtcIntroModalVisible,
      isHasEtcIntroModal,
      depositInfo,
      depositPayInfos,
      productDepositInfo,
      promptInfos,
      // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
      isTecentAds,
      // #endif
    } = this.props;

    const {
      isFinishRender,
      opacity,
      addInstructModalVisible,
      miniDepositModalVisible,
      zhimaModalVisible,
      approveExplainModalVisible,
      approveExplain,
      isShowModifyOrderModal,
      isShowOptimizeStoreModal,
      productConfirmModalVisible,
      optimizationStrengthenModalVisible,
      productConfirmAnchor,
      priceDetailModalVisible,
      osdPriceDetailModalVisible,
      serviceClaimMoreVisible,
      carServiceDetailVisible,
      showServiceDetailCode,
      isShowApplyPenaltyInputModal,
      isShowCouponModal,
      personalInfoAuthModalVisible,
      personalInfoChecked,
      driverLicenseModalVisible,
      showConfirm,
      isShowTableArrow,
      keyboardHeight,
      localContactsModalVisible,
      localContactsInputIsFocus,
      showZhimaModal,
    } = this.state;
    const { amount, canRefund } = resCancelFeeRebook || {};
    const isHasPenalty = canRefund && amount > 0;
    const rebookPenaltyTip = `${rebookPenaltyTipText(`${texts.rmb}${amount}`)}${
      rebookPenalty ? rebookApplyPenaltyTip(`${texts.rmb}${rebookPenalty}`) : ''
    }`;

    const tipText = isHasPenalty ? rebookPenaltyTip : rebookTipText;
    const rentalGuaranteeV2 = getRentalGuaranteeV2(curInsPackageId);
    const asMobileNumber = this.getAsMobileNumber();

    const hasPriceInfo = !!cancelRuleInfo;
    if (isFail) {
      return (
        <NoMatch
          onPressLeft={this.goBack}
          operateButtonPress={this.queryProduct}
        />
      );
    }
    // #ifndef weapp
    const DepositPayment = Utils.getComponentByChannel(
      depositPaymentComponentConfig(isCreditRent()),
    );
    // #endif
    const pageModalProps = {
      visible: addInstructModalVisible,
      onMaskPress: this.onCloseSelected,
      style: styles.shadow,
    };

    // #ifdef weapp alipay 小程序业务差异化：冻结押金弹层 props
    const miniDepositModalProps = {
      visible: miniDepositModalVisible,
      onMaskPress: this.closeMiniDepositModal,
    };

    const zhimaModalProps = {
      visible: zhimaModalVisible,
      onMaskPress: this.closeZhimaModal,
    };
    // #endif

    const handlePressHeaderRight = Utils.isCtripIsd()
      ? this.onPressTandC
      : this.clickSideToolBoxBtn;

    const hasFirstScreenParam = getBookingFirstScreenParam();
    if (isProductLoading && !hasFirstScreenParam) {
      return (
        <BookingLoading pageName={PageType.BookingLoadingAll}>
          <Header
            opacity={opacity}
            onPressLeft={this.goBack}
            onPressRight={handlePressHeaderRight}
          />
        </BookingLoading>
      );
    }
    const couponTestID = this.getCouponTestId({
      hasCoupon: couponList?.usableCoupons?.length ? 1 : 0,
      vendorCode: getBaseResData()?.vendorInfo?.vendorCode,
      vendorId: getBaseResData()?.vendorInfo?.bizVendorCode,
      ifCoupon: couponList?.usableCoupons?.length ? 1 : 0,
      orderGmv: currenctTotalPrice,
      couponPrice: couponList?.selectedCoupon?.deductionAmount,
      couponPromotionId: couponList?.selectedCoupon?.promotionId,
    });

    const activityLogData = this.getActivityLog();

    const activityTestID = this.getActivityTestId({
      hasActivity:
        activityDetail?.promotion &&
        Object.keys(activityDetail?.promotion)?.length
          ? 1
          : 0,
      ifActivity: activityDetail?.status === 0 ? 0 : 1,
      ...activityLogData,
    });

    const BookForm = BookingFormContainer;
    return (
      // prettier-ignore
      <BookingWrapper
        // #ifdef qweapp 去哪儿微信小程序业务差异化：腾讯广告归因
        className={classnames(isTecentAds && 'autoExpose')}
        dataExpose={this.getTecentAdsBuryData()}
        // #endif
      >
        {/* #ifndef weapp alipay 小程序业务差异化：不需要自定义导航栏头部 */}
        <Header
          opacity={opacity}
          toolBoxCustomerJumpUrl={toolBoxCustomerJumpUrl}
          onPressLeft={this.goBack}
          onPressRight={handlePressHeaderRight}
        />
        {/* #endif */}
        <FixedTopBar
          topBarTitle="订单填写"
          onPolicyPress={this.onPressTandC}
          onPressBack={this.goBack}
        />
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          ref={this.scrollView}
          style={xMergeStyles([
            layout.flex1,
            // #ifdef weapp alipay 小程序业务差异化： 设置高度，添加沉浸屏高度
            { height: 0, marginTop: this.topBarInfo.topBarHeight || 0 },
            // #endif
            Utils.isCtripIsd() && isHarmony && { backgroundColor: color.white },
          ])}
          onScrollBeginDrag={Keyboard.dismiss}
          onScroll={this.onScroll}
          scrollEventThrottle={50}
          enableResetScrollToCoords={false}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustContentInsets={false}
          keyboardOpeningTime={100}
          extraHeight={100}
        >
          {/* #ifndef mini 修改订单相关 */}
          {Utils.isCtripIsd() && (
            <ModifyOrderInfoExplain
              modifyRuleExplain={this.props.modifyOrderDesc}
              style={styles.modifyRuleExplainWrap}
              isBook={true}
            />
          )}
          {/* #endif */}
          {Utils.isCtripIsd() ? (
            <BookingVehicleAndVendorInfoVCContainer
              onLocationPress={this.gotoGuidePage}
              onPressLimit={this.onPressLimit}
              onPressVendor={this.onPressVendor}
              isSecretBox={isSecretBox}
              isShowLoading={isProductLoading}
            />
          ) : (
            <>
              {/* #ifndef mini 出境相关 */}
              <VehicleWrapper>
                <VehicleLocation
                  onLocationPress={this.gotoGuidePage}
                  isRefactor={isRefactor}
                />

                <Advantage
                  positivePolicies={positivePolicies}
                  cancelRuleInfo={cancelRuleInfo}
                  confirmInfo={confirmInfo}
                  testID={CarLog.LogExposure({
                    name: '曝光_填写页_激励政策',

                    info: logBaseInfo,
                  })}
                />
              </VehicleWrapper>
              {/* #endif */}
            </>
          )}
          {/* #ifndef mini 修改订单相关 */}
          {/* 出境修改订单温馨提示 */}
          {isRebookOsd && (
            <ModifyOrderInfoExplainOSD
              style={styles.osdModifyRuleExplainWrap}
              originOrderId={ctripOrderId}
              osdModifyOrderNote={osdModifyOrderNote}
              isBook={true}
              modalTitlePrefix="原订单"
            />
          )}
          {/* #endif */}

          {Utils.isCtripIsd() &&
            isPriceLoading &&
            !positivePolicies?.length && (
              <SkeletonLoading
                visible={true}
                style={styles.freeCancelLoadingBg}
                imageStyle={styles.freeCancelLoadingBgHeight}
                pageName={PageType.BookingFreeCancel}
              />
            )}

          <FormWrapper onLayout={this.onLayoutDriver}>
            {Utils.isCtripIsd() && isFinishRender && (
              <AdvantageInfo
                positivePolicies={positivePolicies}
                membershipPerception={membershipPerception}
              />
            )}
            <BookForm
              isShowLoading={isProductLoading}
              onPressDriver={this.onPressDriver}
              onPressQuestion={this.onPressFormQuestion}
              onAddInstructPress={this.onAddInstructFormPress}
              onPressFlightDelayRules={this.onPressFlightDelayRules}
              onPresssMore={this.handlePresssMore}
              onPressDriverLicense={this.showDriverLicenseModal}
              onPressLocalContacts={this.showLocalContactsModal}
              localContactsInputFocus={this.localContactsInputFocus}
            />

            {isFinishRender && hasPriceInfo && Utils.isCtripIsd() && (
              <BookingCouponAndDepositContainer
                couponTestID={couponTestID}
                activityTestID={activityTestID}
                couponList={couponList}
                activityDetail={activityDetail}
                currency={payParams && payParams.currency}
                onPressCoupon={this.onPressCoupon}
                onPressActivity={this.onPressActivity}
                toSelectPassenger={this.onPressDriver}
                isShowLoading={
                  isPriceLoading && !(isFinishRender && hasPriceInfo)
                }
              />
            )}
            {/* #ifdef weapp alipay 小程序业务差异化：添加小程序免押（组件内区分微信支付宝 */}
            <DepositInfo
              depositInfo={depositInfo}
              depositPayInfos={depositPayInfos}
              productDepositInfo={productDepositInfo}
              promptInfos={promptInfos}
              operationHandle={this.operationHandle}
              closeCb={this.queryProduct}
              showZhimaModal={this.showZhimaModal}
              showMiniDepositModal={this.showMiniDepositModal}
            />
            {/* #endif */}
            {isProductLoading && hasFirstScreenParam && (
              <BookingLoading
                pageName={PageType.BookingLoadingHalf}
                style={
                  Utils.isCtripIsd()
                    ? styles.bookingOptimizationLoadingSpace
                    : styles.loadingSpace
                }
              />
            )}
            {/* #ifndef mini 出境相关 */}
            {Utils.isCtripOsd() && isFinishRender && hasPriceInfo && (
              <NewCouponAndActivity
                couponTestID={couponTestID}
                activityTestID={activityTestID}
                couponList={couponList}
                activityDetail={activityDetail}
                adjustPriceDetail={adjustPriceDetail}
                currency={payParams && payParams.currency}
                onPressCoupon={this.onPressCoupon}
                onPressActivity={this.onPressActivity}
                onPressAdjustPrice={this.onPressAdjustPrice}
                isShowLoading={
                  isPriceLoading && !(isFinishRender && hasPriceInfo)
                }
              />
            )}
            {/* #endif */}
            {/* #ifndef weapp */}
            {hasPriceInfo && !isMergeDeposit && (
              <XViewExposure
                onLayout={this.onLayoutDepositPay}
                testID={this.getDepositTestId()}
              >
                <DepositPayment onPressDriver={this.onPressDriver} />
              </XViewExposure>
            )}
            {/* #endif */}
            {/* #ifndef mini */}
            {Utils.isCtripOsd() && (
              <View onLayout={this.onLayoutOsdDepositPay}>
                <DepositMethod logBaseInfo={logBaseInfo} />
              </View>
            )}
            {/* #endif */}
            {Utils.isCtripIsd() && !isProductLoading && isFinishRender && (
              <Insurance
                onPressServiceClaimMore={this.showServiceClaimMore}
                onPressCarServiceDetail={this.showCarServiceDetail}
                ptime={ptime}
                rtime={rtime}
                isShowTableArrow={isShowTableArrow}
              />
            )}
            {isFinishRender && (
              <>
                {/* #ifndef mini */}
                {getOsdCancelRule(cancelRuleInfo, logBaseInfo)}
                {/* #endif */}
                {Utils.isCtripIsd() && (
                  <Extras onPressExtras={this.gotoExtras} />
                )}
                {/* #ifndef mini */}
                {Utils.isCtripOsd() && !!hasExtrasProducts && (
                  <ExtrasInfoV2Container
                    style={styles.extrasInfo}
                    onPressExtras={this.gotoExtrasV2}
                  />
                )}
                {/* #endif */}
                {Utils.isCtripIsd() ? (
                  <BlockCard
                    title={invoiceInfo?.title}
                    desc={invoiceInfo?.description}
                    testID={CarLog.LogExposure({
                      name: '曝光_填写页_发票模块',
                    })}
                  />
                ) : (
                  <>
                    {/* #ifndef mini 出境相关 */}
                    <InvoiceInfo
                      invoiceInfo={invoiceInfo}
                      testID={CarLog.LogExposure({
                        info: logBaseInfo,
                        name: '曝光_填写页_发票模块',
                      })}
                    />
                    {/* #endif */}
                  </>
                )}
              </>
            )}
          </FormWrapper>
          {isFinishRender && (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_填写页_租车服务提供方',
              })}
            >
              <GpdrContainer
                onPressTandC={this.onPressTandC}
                onPressCtripDepositFee={this.onPressCtripDepositFee}
                showCtripDepositFee={isCreditRentPayType(depositPayType)}
                onPressExplain={this.showApproveExplainModal}
                isSecretBox={isSecretBox}
                showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
                onPressSelfServiceInstruction={
                  this.onPressSelfServiceInstruction
                }
              />
            </XViewExposure>
          )}
          {/* 安心订放心行 */}
          {isFinishRender && (
            <>
              {/* 小程序业务差异化：替换填写页底部 slogan */}
              {MiniChannel.isMini() ? (
                <BottomWrap isTabISD={true} isBooking={true} />
              ) : (
                <View
                  className={c2xStyles.bookingOptimizationRelievedContainer}
                >
                  {/* #ifndef mini */}
                  <MarketingFooter
                    wrapStyle={styles.bookingOptimizationRelievedBookingMtNew}
                    bottomImg={`${ImageUrl.DIMG04_PATH}1tg4c12000dg92m3rDFED.png`}
                  />
                  {/* #endif */}
                </View>
              )}
            </>
          )}
        </KeyboardAwareScrollView>
        {!!(
          !!asMobileNumber &&
          localContactsInputIsFocus &&
          keyboardHeight
        ) && (
          <AsMobileBlock
            setAsMobile={() => this.setAsMobile(asMobileNumber)}
            mobile={asMobileNumber}
            style={{ bottom: keyboardHeight }}
          />
        )}
        {!isProductLoading &&
          (Utils.isCtripIsd() ? (
            <BookingFooter
              onPressBtn={this.handleBookPress}
              onPressBar={this.onPressBar}
              buttonTestID={UITestID.car_testid_page_booking_footer_button}
              barTestID={UITestID.car_testid_page_booking_footer_bar}
              renderTip={
                isRebook ? (
                  <RebookTip
                    isHasPenalty={isHasPenalty}
                    isHasReBookPenalty={!!rebookPenalty}
                    tip={tipText}
                    showApplyPenaltyInputModal={this.showApplyPenaltyInputModal}
                  />
                ) : (
                  !this.state.priceDetailModalVisible && <DepositTip />
                )
              }
              isShowFreeCancelLabel={cancelRuleInfo?.showFree}
              buttonTextStyle={styles.bookingOptimizationButtonTextStyle}
              isShowTotalAmount={true}
              showPriceDetailModal={priceDetailModalVisible}
              style={styles.footBarWrap}
            />
          ) : (
            <>
              {/* #ifndef mini */}
              <BookFooterContainer
                onPressBtn={this.handleBookPress}
                onPressBar={this.onPressBar}
                onCheckBarCheck={this.onCheckBarCheck}
                onCheckBarPress={this.showApproveExplainModal}
                showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
                personalInfoCheck={personalInfoChecked}
              />
              {/* #endif */}
            </>
          ))}
        {isFinishRender && (
          <BookingModalsContainer
            onGotoOrderDetail={this.goToOrder}
            onConfirmFightNo={this.onConfirmFightNo}
            onCancelFightNo={this.onCancelFightNo}
            onPressCouponModalButton={this.onPressCouponModalButton}
            fromPage={fromPage}
          />
        )}
        {/* #ifndef mini 小程序业务差异化：18631/AddSnapShot未使用redux注释 */}
        {this.state.isSnapShotRender &&
          (Utils.isCtripIsd() ? (
            <BookingSnapShot
              productConfirmRef={this.materialModalRef}
              priceDetailsRef={this.priceDetailsRef}
            />
          ) : (
            <ProductSnapShotContainer
              isProductLoading={false}
              selectedIdType={selectedIdType}
              materialModalRef={this.materialModalRef}
              priceDetailsRef={this.priceDetailsRef}
              insuranceSuitsModalRef={this.insuranceSuitsModalRef}
              curInsPackageId={curInsPackageId}
            />
          ))}
        {/* #endif */}
        {Utils.isCtripIsd() && (
          <CouponModal
            onSelected={changeCoupon}
            visible={isShowCouponModal}
            onCancel={this.closeCouponModal}
            renderData={couponList}
          />
        )}
        <BookingPriceChangePop
          onClose={this.closePriceChangePop}
          onBackToList={this.handlePriceChangeLeftButton}
          onSubmit={this.handlePriceChangeRightButton}
          onExposure={this.onPriceChangeExposure}
        />
        {/* #ifndef mini 屏蔽距离地图测算 */}
        {Utils.isCtripIsd() && needDownGrade && isFinishRender && (
          <Distance
            onPickupDriveTimeCalculated={this.onPickupDriveTimeCalculated}
          />
        )}
        {/* #endif */}
        {isFinishRender && (
          <>
            {/* #ifndef weapp alipay 小程序业务差异化：注释不用的组件-取消重订填写违约金的功能 */}
            {isHasPenalty && (
              <ApplyPenaltyInputModal
                orderId={ctripOrderId}
                setRebookPenalty={setRebookPenalty}
                rebookPenalty={rebookPenalty}
                visible={isShowApplyPenaltyInputModal}
                onMaskPress={this.hideApplyPenaltyInputModal}
                resCancelFee={resCancelFeeRebook}
                headerDom={
                  <RebookTip
                    isHasPenalty={isHasPenalty}
                    isHasReBookPenalty={!!rebookPenalty}
                    isShowApplyPenaltyInputModal={isShowApplyPenaltyInputModal}
                    tip={tipText}
                    showApplyPenaltyInputModal={this.hideApplyPenaltyInputModal}
                  />
                }
              />
            )}
            {/* #endif */}
            {/* #ifndef mini 下单时候的同意协议组件 */}
            <PersonalAuthCheckModal
              visible={personalInfoAuthModalVisible}
              onMaskPress={this.hidePersonalInfoAuthModal}
              style={styles.shadow}
              onPressBtn={this.handleBookPress}
              onCheckBarCheck={this.onCheckBarCheck}
              onCheckBarPress={this.showApproveExplainModal}
              buttonName={agreeSubmitName}
              personalInfoCheck={personalInfoChecked}
              showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
            />

            <ApproveExplainModal
              visible={approveExplainModalVisible}
              onMaskPress={this.hideApproveExplainModal}
              style={styles.shadow}
              data={approveExplain}
              onPressBtn={this.getApproveExplainModalBtnFn()}
              hasFooterBtn={remoteQConfig?.personalInfoAuthCheck}
              buttonName={
                (personalInfoChecked || personalInfoAuthModalVisible) &&
                agreeSubmitName
              }
            />
            {/* #endif */}

            <HalfPageModal {...this.getActivityNoteModalProps()} />

            {/* 调整价格说明 */}
            <HalfPageModal
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...this.getAdjustPriceNoteModalProps()}
            />

            {Utils.isCtripIsd() && (
              <ProductConfirmModalNew
                page={this}
                visible={productConfirmModalVisible}
                anchor={productConfirmAnchor}
                showFooter={false}
                onPressEasyLife={this.showEasyLifeModal}
                onClose={this.closeProductConfirmModal}
                onPressOptimize={this.showOptimizeStoreModal}
                showOptimizationStrengthenModal={
                  this.openOptimizationStrengthenModal
                }
                isSecretBox={isSecretBox}
                pageName="Booking"
              />
            )}
            {Utils.isCtripIsd() && (
              <ServiceClaimMoreModal
                visible={serviceClaimMoreVisible}
                onCancel={this.hideServiceClaimMore}
                data={rentalGuaranteeV2?.vendorServiceDetail}
              />
            )}
            {Utils.isCtripIsd() && (
              <CarServiceDetailModal
                visible={carServiceDetailVisible}
                onCancel={this.hideCarServiceDetail}
                data={rentalGuaranteeV2?.packageDetailList}
                purchasingNotice={rentalGuaranteeV2?.purchasingNotice}
                ptime={ptime}
                rtime={rtime}
                selectedInsuranceIds={addOnCodes}
                changeSelectInsurance={changeSelectInsurance}
                fromPage={CarServiceFromPageTypes.booking}
                showServiceDetailCode={showServiceDetailCode}
                haveFooter={!isEasyLife2024}
              />
            )}

            <BookingEasyLifeModal />
            <BookingPriceDetail
              visible={priceDetailModalVisible}
              onClose={this.closePriceDetailModal}
              onContinue={this.closePriceDetailModal}
              style={styles.priceDetailStyle}
              role={PageRole.BOOKING}
            />
            {/* #ifndef mini 修改订单相关 */}
            {Utils.isCtripOsd() && (
              <PriceDetailModalOsd
                visible={osdPriceDetailModalVisible}
                role={PageRole.BOOKING}
                data={getFeeDetailData()}
                onPressBtn={this.handleBookPress}
                onClose={this.closeOsdPriceDetailModal}
              />
            )}
            {/* #endif */}
            {/* <SesameContainer /> */}
            {/* #ifndef mini 修改订单相关 */}
            {isShowModifyOrderModal && (
              <ModifyOrderModal {...this.getModifyOrderModalData()} />
            )}
            {/* #endif */}
            <StoreModal
              onMaskPress={this.hideOptimizeStoreModal}
              visible={isShowOptimizeStoreModal}
            />

            <EhiFreeDepositRuleModalContainer isBooking={true} />
            {/* #ifndef mini 出境相关 */}
            {/* 境外免押规则弹层 */}
            {Utils.isCtripOsd() && <FreeDepositRuleModalContainer />}
            {/* #endif */}
            {/* #ifndef weapp 芝麻相关 */}
            <CancelZhimaModal />
            {/* #endif */}
            {Utils.isCtripIsd() && (
              <PriceAlert
                visible={isShowPriceConfirm}
                priceAlertHandler={this.priceAlertHandler}
                popToVendorList={this.popToVendorList}
              />
            )}
            {Utils.isCtripIsd() && <PickupDownGradePop />}
            <VendorListOptimizationStrengthenModal
              visible={optimizationStrengthenModalVisible}
              onCancel={this.closeOptimizationStrengthenModal}
              isSecretBox={isSecretBox}
              pageName="Booking"
            />

            {Utils.isCtripIsd() && !isHasEtcIntroModal && (
              <EtcIntroModalContainer
                visible={isEtcIntroModalVisible}
                onClose={this.handleEtcIntroModalClose}
              />
            )}
            {/* #ifndef mini 保代相关 */}
            <BookingCreateInsLoading />
            <BookingCreateInsFailPop
              onBack={this.handleCreateInsFailPopLeftButton}
              onPay={this.handleCreateInsFailPopRightButton}
            />
            {/* #endif */}
            <AddInstructModal
              pageModalProps={pageModalProps}
              addInstructData={addInstructData}
            />

            {/* #ifdef weapp alipay 小程序业务差异化：冻结押金弹层 */}
            <MiniDepositModal
              pageModalProps={miniDepositModalProps}
              promptInfos={promptInfos}
            />
            {/* #endif */}
            {/* #ifdef alipay 小程序业务差异化：芝麻免押反馈弹层 */}
            <SesameFeedback closeCb={this.queryProduct} />
            {/* #endif */}
            {/* #ifndef mini 出境相关 */}
            {Utils.isCtripOsd() && <DepositRateDescriptionModal />}
            {Utils.isCtripOsd() && isKlb && (
              <FlightDelayRulesModal
                testID={CarLog.LogExposure({
                  name: '曝光_填写页_航班延误政策_曝光弹层',

                  info: logBaseInfo,
                })}
              />
            )}
            {/* #endif */}
            {Utils.isCtripIsd() && (
              <BookingConfirmModal
                visible={showConfirm}
                onConfirm={this.closeBookingConfirmModal}
                onCancel={this.closeBookingConfirmModalWithPop}
              />
            )}
            <Block>
            {Utils.isCtripIsd() && (
              <BusinessLicenseModal
                visible={isBusinessLicenseModalVisible}
                onClose={this.handleBusinessLicenseClose}
              />
            )}
            </Block>

            {/* #ifndef mini 出境相关 */}
            {Utils.isCtripOsd() && (
              <FuelDescriptionModalContainer
                visible={isFuelDescriptionModalVisible}
                onClose={this.handleFuelDescClose}
              />
            )}
            {Utils.isCtripOsd() && (
              <LocalContactsModal
                visible={localContactsModalVisible}
                onClose={this.hideLocalContactsModal}
                onSelectContact={this.onSelectContactType}
                pickUpAreaCode={pickUpAreaCode}
              />
            )}
            {/* #endif */}
            {/* 小程序业务差异化：芝麻免押流程弹层 */}
            {/* #ifdef weapp */}
            <ZhimaIntroModal
              pageModalProps={zhimaModalProps}
              depositInfo={productDepositInfo}
            />
            {/* #endif */}
          </>
        )}
        {this.props.isDebugMode && (
          <AssistiveTouch
            onPress={() => {
              this.push('Debug');
            }}
          />
        )}
      </BookingWrapper>
    );
  }
}

export default Booking;
