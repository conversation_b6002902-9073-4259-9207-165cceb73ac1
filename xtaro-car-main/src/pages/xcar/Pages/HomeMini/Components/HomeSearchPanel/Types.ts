// import { IMarketThemeHome } from '../../../state/home/<USER>';

export interface ILocation {
  locationName: string;
  cityId?: number;
  latitude?: number;
  longtitude?: number;
  locationCode?: string;
  locationType?: number;
  id?: string;
  countryId?: number;
  countryName?: string;
  provinceId?: number;
  provinceName?: string;
  cityName?: string;
  timezone?: number;
}

export interface IPropsType {
  rentalLocation: any;
  pcity: ILocation;
  rcity: ILocation;
  ptime: string;
  rtime: string;
  timeWarning?: string;
  insufficientTimeWarning?: string;
  showDropoff: boolean;
  diffLocationWarning: string;
  isPickupDoor?: boolean;
  isShowPickupDoor?: boolean;
  isZhimaChecked?: boolean;
  //   homeMarketTheme?: IMarketThemeHome;
  homeMarketTheme?: any;
  marketThemeEnabled?: boolean;
  isShowFreeDeposit?: boolean;
  showBusinessLicense?: any;
  setLocationInfo: (data: any) => void;
  updatePickupDoor: (data: any) => void;
  setDateInfo: (data: any) => void;
  onPressSearch: () => void;
  onDateClick: (type: string) => void;
  preFetchListProducts: (data: any) => void;
  fetchCityList: (data?: any) => void;
  fetchAreaList: (data?: any) => void;
  setTempLocation: (data: any) => void;
  isShowNumberAndAge?: boolean; // 年龄、人数选择器
  adultSelectNum: number; // 成人数
  childSelectNum: number; // 儿童数
  adultMaxNum?: number; // 成人最大数
  age: string; // 年龄
  setAgeAdultAndChildNum: (data: any) => void; // 设置成人数、儿童数
  setAge: (data: any) => void; // 设置年龄
  isShowBottomTip?: boolean; // 退赔安心、中文服务
  isLazyLoad?: boolean;
  onPickChangeCallBack?: (start: boolean) => void; //监听pickerModal滚动
  isCustomizationHome: boolean;
  searchBtnText?: string; //搜索按钮文案
  businessLicenseVisible?: boolean; //是否需要需要展示营业执照模块
  paddingTop?: number;
  isHomeSearchPanel?: boolean; // 兼容门店首页 searchPanel 样式
  filterItems?: Array<any>;
  setFilterItems?: (data: any) => void;
  setPickType?: (data: any) => void;
  preListPageFetch: (data: any) => void;
  pickType: string;
  isBusinessLicenseModalVisible?: boolean;
  setBusinessLicenseModalVisible: (data: any) => void;
  showSubsidyImage?: boolean; // 是否展示国内超级补贴
}

export interface RenderFilterItemsProps {
  nFilterItems?: Array<any>;
  onPressFilter: (data) => void;
}

export interface INumItem {
  title: string;
  content: string;
  iconCode?: string;
  onClick: () => void;
  onClickIcon?: () => void;
}
