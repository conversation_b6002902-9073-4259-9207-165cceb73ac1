@import '@tokenColorScss';
.search-fp {
  padding-bottom: 18px;
  background: #fff;
  position: relative;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  &.custom {
    border-radius: 16px;
  }
  &.bottom-0 {
    padding-bottom: 0;
  }
  &-free-deposit {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    color: #999;
    &-license-text {
      margin-left: 8px;
      font-size: 18px;
      line-height: 24px;
    }
  }
  .numberAndAge {
    display: flex;
    padding: 24px 0;
  }
  .numWrap {
    flex: 1;
  }
  .numTitleText {
    font-family: PingFangSC-Regular;
    font-size: 26px;
    color: #999;
    letter-spacing: 0;
    line-height: 36px;
    margin-bottom: 4px;
  }
  .numbox {
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .numContentText {
    font-family: PingFangSC-Medium;
    font-size: 40px;
    line-height: 44px;
    color: #333333;
    letter-spacing: 0;
    font-weight: bold;
  }
  .numIcon {
    font-size: 30px;
    color: #333333;
    width: 30px;
    height: 30px;
  }
}

.earch-form-inner {
  padding: 0 32px;
}
.pickup-dw {
  padding: 24px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.pickup-dw .icon-round-checkbox,
.pickup-dw .icon-round-checkbox-checked {
  font-size: 32px;
  margin-right: 14px;
  margin-top: 2px;
  /* #ifdef alipay */
  margin-top: 5px;
  /* #endif */
}

.pickup-dw .icon-square-checkbox,
.pickup-dw .icon-square-checkbox-checked {
  font-size: 32px;
  text-shadow: 0px 2px 8px rgba(226, 226, 226, 0.5);
  margin-right: 12px;
  margin-top: 2px;
  /* #ifdef alipay */
  margin-top: 5px;
  /* #endif */
}

.pickup-dw .icon-square-checkbox {
  color: #ccc;
}

.pickup-dw .icon-round-checkbox {
  color: #aaa;
}
.pickup-dw .icon-round-checkbox-checked,
.pickup-dw .icon-square-checkbox-checked {
  color: $blueBase;
}
.pickup-dw .title {
  font-size: 26px;
  color: #333333;
  line-height: 34px;
}
.search-panel-btn-box {
  margin-bottom: 18px;
  margin-top: 4px;
  position: relative;
}
.search-btn-box .search-button {
  margin: 0 30px;
  font-size: 34px;
  border-radius: 12px;
  background: -webkit-gradient(
    linear,
    0% 0%,
    100% 100%,
    from($linearGradientOrangef),
    to($linearGradientOrangeb)
  );
  color: #fff;
  font-weight: bold;
  padding: 2px 0;
  border: none;
}
.search-btn-box .search-button:active {
  opacity: 0.9;
}
.search-dybg-box {
  flex: 1;
  height: 100%;
  background-size: 100% 100%;
  padding: 100px 6px 14px 24px;
  border-radius: 16px;
  position: relative;
  margin-top: -13px;
  margin-bottom: -13px;
}

.search-dyimg-box {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.wed-btn {
  .custom-btn {
    width: 100%;
    height: 110px;
    background: url('https://pages.c-ctrip.com/carisd/weixin/wed-search-btn.png')
      no-repeat 0 0;
    background-size: cover;
    color: transparent;
    &::after {
      display: none;
    }
    border: none;
  }
  // .search-btn-shadow {
  //     background: url('https://pages.c-ctrip.com/carisd/miniapp/new-wed-shadow.png') no-repeat;
  //     background-size: 100% 100%;
  //     width: 100%;
  //     height: 110px;
  //     position: absolute;
  //     margin-top: 15px;
  // }
}
.hol-btn {
  .custom-btn {
    width: 100%;
    height: 110px;
    background: url('https://pages.c-ctrip.com/carisd/miniapp/wuyi.png')
      no-repeat 0 0;
    background-size: cover;
    color: transparent;
    &::after {
      display: none;
    }
  }
  .search-btn-shadow {
    background: url('https://pages.c-ctrip.com/carisd/miniapp/zuche-shad.png')
      no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 110px;
    position: absolute;
    margin-top: 15px;
  }
}

.license-gotoIcon {
  font-size: 20px;
  display: flex;
  align-items: center;
}
.place-holder {
  margin-top: 44px;
}

.age-title {
  font-size: 34px;
  font-weight: bold;
  font-family: PingFangSC-Semibold;
  color: #333333;
}
.age-tips {
  padding: 40px 32px 130px;
  background-color: #fff;
  font-size: 30px;
}
.bottom-tip {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 90px;
  .icon-arrow-right {
    display: flex;
    align-items: center;
    margin-top: 3px;
    margin-left: 4px;
  }
}

.stip-box {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  line-height: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
}
.stip-img {
  margin-right: 16px;
  width: 32px;
  height: 32px;
}
.stip-line {
  width: 1px;
  height: 26px;
  background-color: #dddddd;
}
.subsidy-tag-normal {
  width: 149px;
  height: 50px;
  position: absolute;
  right: 30px;
  top: -34px;
}
.subsidy-tag-market {
  width: 149px;
  height: 50px;
  position: absolute;
  right: 46px;
  top: -18px;
}
.subsidy-tag-market-wx-ios {
  width: 149px;
  height: 50px;
  position: absolute;
  right: 47px;
  top: -18px;
}
.subsidy-tag-market-bg {
  width: 149px;
  height: 50px;
  position: absolute;
  right: 32px;
  top: -18px;
}
