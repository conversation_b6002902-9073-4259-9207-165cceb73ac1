import React, { useCallback, memo, useState, useRef, useEffect } from 'react';
import { cloneDeep } from 'lodash-es';
import classnames from 'classnames';
import { XView as View } from '@ctrip/xtaro';
import Image from '@c2x/components/Image';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkCheckBox from '@ctrip/rn_com_car/dist/src/Components/Basic/Checkbox';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import CarLog from '../../../../Util/CarLog';
import { IPropsType, RenderFilterItemsProps } from './Types';
import LocationSelect from '../LocationSelect/Index';
import DateSelect from '../DateSelect/Index';
import { getLocalDayJs } from '../../../../Components/Calendar/Method';
// import PickerModal from '../pickerModal';
// import { ageData } from './helpers';
// import Modal from '../../basic/modal';
// import { ADULT_MAX_NUM } from '../../../constants/frontEndConfig';
// import { tips } from './config';
// import { URL_PREFIX } from '../../../difference/constants/constant';
// import ProcImage from '../procImage';
import BusinessLicense from '../../../../Containers/HomeMiniBusinessLicenseContainer';
import BusinessLicenseModal from '../../../../Containers/BusinessLicenseModalContainer';
import CButton from '../CButton/Index';

import { Utils } from '../../../../Diff/utils';
import { Utils as Util } from '../../../../Util/Index';
import './index.scss';
import CalendarModal from '../../../../Components/Calendar/Index';
import { composeCityArea2RentalLocation } from '../../../../State/LocationAndDate/Mappers';
import Texts from '../../../Home/Texts';
import { ImageUrl } from '../../../../Diff/constants';

const { getPixel, isIos } = BbkUtils;

// TODO: 去哪儿小程序屏蔽出境
// const NumItem: React.FC<INumItem> = memo(
//   ({ title, content, onClick, onClickIcon }) => {
//     return (
//       <View
//         className="numWrap"
//         onClick={onClick}
//         // data-testid={UITestID.car_testid_page_home_driver_age_num}
//       >
//         <Text className="numTitleText">{title}</Text>
//         <View className="numbox">
//           <Text
//             className="numContentText"
//             // data-testid={UITestID.car_testid_page_home_driver_age}
//           >
//             {content}
//           </Text>
//           {!!onClickIcon && (
//             <Text
//               className="icon-help numIcon"
//               onClick={e => {
//                 e.stopPropagation();
//                 onClickIcon();
//               }}
//               // data-testid={UITestID.car_testid_page_home_driver_age_help}
//             />
//           )}
//         </View>
//       </View>
//     );
//   },
// );

export const RenderFilterItems: React.FC<RenderFilterItemsProps> = memo(
  ({ nFilterItems, onPressFilter }) => (
    <View className="pickup-dw">
      {nFilterItems?.map(item => (
        <BbkCheckBox
          key={item?.name}
          checked={item?.isSelected}
          text={item?.name}
          iconStyle={{
            fontSize: getPixel(32),
            paddingRight: getPixel(16),
          }}
          squareIcon={icon.newSquare}
          squareTickFilledIcon={icon.newSquareTickFilled}
          onCheckedChange={() => {
            onPressFilter(item);
          }}
          // testID={getTestID(item.code)}
        />
      ))}
    </View>
  ),
);

const SearchPanel: React.FC<IPropsType> = ({
  rentalLocation = { dropOff: '', pickUp: '' },
  pcity,
  rcity,
  ptime,
  rtime,
  showDropoff,
  diffLocationWarning,
  timeWarning,
  insufficientTimeWarning,
  isShowPickupDoor = true,
  homeMarketTheme,
  marketThemeEnabled = false,
  setLocationInfo,
  onPressSearch,
  // onDateClick,
  // preFetchListProducts,
  // fetchCityList,
  // fetchAreaList,
  // setTempLocation,
  // isShowNumberAndAge = false,
  // adultSelectNum,
  // childSelectNum,
  // adultMaxNum = ADULT_MAX_NUM,
  // adultMaxNum = 15,
  // age,
  // setAge,
  // setAgeAdultAndChildNum,
  // isShowBottomTip,
  isLazyLoad,
  // onPickChangeCallBack,
  isCustomizationHome,
  searchBtnText = '立即租车',
  paddingTop,
  isHomeSearchPanel = false,
  filterItems,
  setFilterItems,
  setPickType,
  pickType,
  preListPageFetch,
  setDateInfo,
  isBusinessLicenseModalVisible,
  setBusinessLicenseModalVisible,
  showSubsidyImage = false,
}) => {
  const [btnClass, setBtnClass] = useState('');
  const [showCalendar, setShowCalendar] = useState(false);
  const CalendarModalRef = useRef(null);
  const pickTypeRef = useRef(pickType);

  // TODO: 去哪儿小程序屏蔽出境
  // const [numberModalVisible, setNumberModalVisible] = useState(false);
  // const [ageModalVisible, setAgeModalVisible] = useState(false);
  // const [ageIconModalVisible, setAgeIconModalVisible] = useState(false);
  // const [curChildMaxNum, setCurChildMaxNum] = useState(
  //   adultMaxNum - adultSelectNum,
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // 成人数量 1-15
  // const adultRange = useMemo(() => Utils.range(1, adultMaxNum), [adultMaxNum]);

  // TODO: 去哪儿小程序屏蔽出境
  // 儿童数量 0-max(max=15-成人数量)
  // const childRange = useMemo(
  //   () => Utils.range(0, curChildMaxNum),
  //   [curChildMaxNum],
  // );

  const onCheckChange = useCallback(
    isShowDropOff => {
      setLocationInfo({
        isShowDropOff,
        dropOff: !isShowDropOff
          ? rentalLocation.pickUp
          : rentalLocation.dropOff,
      });
      CarLog.LogCode({
        enName: 'C_HOME_CHANGEINFO_POP_SWITCH_DROPOFF',
        name: '点击_首页_修改取还车信息_异地还车按钮',
        isShowDropOff,
      });
    },
    [rentalLocation.dropOff, rentalLocation.pickUp, setLocationInfo],
  );

  const onPressFilter = useCallback(
    item => {
      CarLog.LogCode({
        enName: `C_HOME_FILTERITEM_PRESS_${item.name}`,
        // TODO: 临时添加 name，解决报错
        name: `C_HOME_FILTERITEM_PRESS_${item.name}`,
      });
      const newFilterItems = cloneDeep(filterItems);
      const index = filterItems?.findIndex(m => m?.code === item.code);

      if (index !== -1 && Utils.isObject(newFilterItems[index])) {
        newFilterItems[index].isSelected = !newFilterItems[index].isSelected;
      }

      setFilterItems?.({ filterItems: newFilterItems });

      // 预请求列表页
      const filters = [].concat(
        newFilterItems.filter(newItem => newItem.isSelected).map(v => v.code),
      );
      preListPageFetch({ filters });
    },
    [filterItems, setFilterItems, preListPageFetch],
  );

  const onPressCity = isPickUp => {
    CarLog.LogCode({
      enName: isPickUp
        ? 'C_HOME_CHANGEINFO_POP_PICKUP_CITY'
        : 'C_HOME_CHANGEINFO_POP_DROPOFF_CITY',
      name: isPickUp
        ? '点击_首页_修改取还车信息_修改取车城市信息'
        : '点击_首页_修改取还车信息_修改还车城市信息',
    });
  };

  const onPressLocation = isPickUp => {
    CarLog.LogCode({
      enName: isPickUp
        ? 'C_HOME_CHANGEINFO_POP_PICKUP_LOCATION'
        : 'C_HOME_CHANGEINFO_POP_DROPOFF_LOCATION',
      name: isPickUp
        ? '点击_首页_修改取还车信息_修改取车地点信息'
        : '点击_首页_修改取还车信息_修改还车地点信息',
    });
  };

  const handleAreaPress = useCallback(
    data => {
      const rentalLocationTemp = composeCityArea2RentalLocation(data);
      const locationInfo = {
        [pickTypeRef.current]: rentalLocationTemp,
      };
      setTimeout(() => {
        setLocationInfo(locationInfo);
      });
    },
    [setLocationInfo],
  );

  const handleBusinessLicenseClose = useCallback(() => {
    setBusinessLicenseModalVisible(false);
  }, [setBusinessLicenseModalVisible]);

  useEffect(() => {
    if (!marketThemeEnabled) {
      // 如果不允许营销皮肤，不设置按钮特殊样式
      return;
    }
    const day = new Date(); // 将日期值格式化
    const minLimitDay = new Date('2022/04/20 00:00:00').getTime();
    const activeLimitDay = new Date('2022/05/08 23:59:59').getTime();
    const isOverLimit =
      minLimitDay < day.getTime() && day.getTime() < activeLimitDay;
    // 优先周三活动日
    if (new Date().getDay() === 3) {
      // 周三btn样式
      setBtnClass('wed-btn');
    } else if (isOverLimit) {
      setBtnClass('hol-btn');
    }
  }, [marketThemeEnabled]);

  useEffect(() => {
    pickTypeRef.current = pickType;
  }, [pickType]);

  // TODO: 去哪儿小程序屏蔽出境
  // const onPressNumber = useCallback(
  //   debounce((visible = true) => {
  //     setNumberModalVisible(visible);
  //   }),
  //   [],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const onPressAge = useCallback(
  //   debounce((visible = true) => {
  //     setAgeModalVisible(visible);
  //   }),
  //   [],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const onPressAgeIcon = useCallback(
  //   debounce((visible = true) => {
  //     setAgeIconModalVisible(visible);
  //   }),
  //   [],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const onNumberChange = useCallback(
  //   ([aIndex, cIndex]) => {
  //     const cMaxNum = adultMaxNum - adultRange[aIndex];
  //     const childSelectNum = childRange[cIndex];
  //     const newChildSelectNum =
  //       childSelectNum > cMaxNum ? cMaxNum : childSelectNum;
  //     setCurChildMaxNum(cMaxNum);
  //     return newChildSelectNum;
  //   },
  //   [adultRange, childRange],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const onConfirmNum = useCallback(
  //   debounce((aIndex, cIndex) => {
  //     const adultSelectNum = adultRange[aIndex];
  //     const childSelectNum = childRange[cIndex];

  //     setAgeAdultAndChildNum({
  //       adultSelectNum,
  //       childSelectNum,
  //     });
  //     onPressNumber(false);
  //   }),
  //   [adultRange, childRange],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const onConfirmAge = useCallback(
  //   debounce(aIndex => {
  //     const age = ageData?.list[aIndex];

  //     setAge({
  //       age,
  //     });
  //     onPressAge(false);
  //   }),
  //   [],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const goTipPage = useCallback(
  //   debounce(url => {
  //     openWebview(url);
  //   }),
  //   [],
  // );

  // TODO: 去哪儿小程序屏蔽出境
  // const ageIndex = useMemo(() => ageData.list.findIndex(i => i === age), [age]);

  useEffect(() => {
    if (showCalendar) {
      if (CalendarModalRef?.current) {
        CalendarModalRef.current.showCalendar();
      } else {
        setShowCalendar(false);
      }
    }
  }, [showCalendar]);

  const handleDateClick = useCallback(() => {
    setShowCalendar(true);
  }, [setShowCalendar]);

  const handleTimepassed = useCallback(
    (pickupTime, returnTime) => {
      const curMinutes =
        Math.ceil(parseInt(dayjs().format('mm'), 10) / 15) * 15;
      const timeDiff =
        Math.ceil(dayjs(returnTime).diff(dayjs(pickupTime), 'minute') / 15) *
        15;
      const newRentalDate = {
        pickup: dayjs().add(4, 'hours').minute(curMinutes),
        dropoff: dayjs()
          .add(4, 'hours')
          .minute(curMinutes + timeDiff),
      };
      setDateInfo(newRentalDate);
      Toast.show('取车时间已过当前时间，已为您修改取车时间', 2);
    },
    [setDateInfo],
  );

  const onConfirmTime = useCallback(
    data => {
      const { ptime: newPtime, rtime: newRtime } = data;
      const newRentalDate = { pickup: newPtime, dropoff: newRtime };
      if (dayjs(newPtime).diff(getLocalDayJs(), 'seconds') < 0) {
        handleTimepassed(newPtime, newRtime);
      } else {
        setDateInfo(newRentalDate);
      }
    },
    [handleTimepassed, setDateInfo],
  );
  return (
    <>
      <View
        className={classnames(
          'search-fp',
          // TODO: 去哪儿小程序屏蔽出境
          // isShowBottomTip && 'bottom-0',
          isCustomizationHome && isHomeSearchPanel && 'custom',
        )}
        style={{ paddingTop }}
        // data-testid={UITestID.car_testid_page_search_form_wrap}
      >
        <View className="earch-form-inner">
          <LocationSelect
            diffLocationWarning={diffLocationWarning}
            isShowDropOff={showDropoff}
            onCheckChange={onCheckChange}
            onPressCity={onPressCity}
            onPressLocation={onPressLocation}
            isCustomizationHome={isCustomizationHome}
            isHomeSearchPanel={isHomeSearchPanel}
            setPickType={setPickType}
            pcity={pcity}
            rcity={rcity}
            handleAreaPress={handleAreaPress}
          />
          <DateSelect
            ptime={ptime}
            rtime={rtime}
            insufficientTimeWarning={insufficientTimeWarning}
            timeWarning={timeWarning}
            onDateClick={handleDateClick}
          />
          {showCalendar && (
            <CalendarModal
              ref={CalendarModalRef}
              ptime={ptime}
              rtime={rtime}
              maxMonths={Util.getRentalMaxMonth()}
              onConfirm={onConfirmTime}
              setShowCalendar={setShowCalendar}
            />
          )}
          <View>
            {isShowPickupDoor && (
              <RenderFilterItems
                nFilterItems={filterItems}
                onPressFilter={onPressFilter}
              />
            )}
          </View>
          {/* TODO: 去哪儿小程序屏蔽出境 */}
          {/* {isShowNumberAndAge && (
            <View className="numberAndAge">
              <NumItem
                title="人数"
                content={`${adultSelectNum}成人 + ${childSelectNum}儿童 `}
                onClick={() => onPressNumber(true)}
              />
              <NumItem
                title="驾驶员年龄"
                // '%1$s 周岁 review shark'
                content={`${age} 周岁`}
                onClick={() => {
                  onPressAge(true);
                }}
                onClickIcon={() => {
                  onPressAgeIcon(true);
                }}
              />
            </View>
          )} */}
        </View>
        {/* 立即租车按钮 */}
        <View className="search-panel-btn-box">
          {marketThemeEnabled && homeMarketTheme?.searchUrl ? (
            <View className="search-dybg-box" onClick={onPressSearch}>
              <View className="search-btn-shadow" />
              <Image
                className="search-dyimg-box"
                mode="scaleToFill"
                src={homeMarketTheme && homeMarketTheme.searchUrl}
              />
              {Util.isCtripIsd() && showSubsidyImage && (
                <Image
                  className={classnames(
                    'subsidy-tag-market',
                    /* #ifdef weapp */
                    isIos && 'subsidy-tag-market-wx-ios',
                    /* #endif */
                  )}
                  mode="scaleToFill"
                  src={`${ImageUrl.DIMG04_PATH}1tg6b12000lqisxlaD24A.png`}
                />
              )}
            </View>
          ) : (
            <>
              <View
                className={classnames('search-btn-box', btnClass)}
                onClick={onPressSearch}
                // data-testid={UITestID.car_testid_page_home_search_btn}
              >
                <View className="search-btn-shadow" />
                <CButton
                  debounce={true}
                  debounceTime={300}
                  className={btnClass ? 'custom-btn' : 'search-button'}
                >
                  {searchBtnText}
                </CButton>
              </View>
              {Util.isCtripIsd() && showSubsidyImage && (
                <Image
                  className={
                    btnClass ? 'subsidy-tag-market-bg' : 'subsidy-tag-normal'
                  }
                  mode="scaleToFill"
                  src={`${ImageUrl.DIMG04_PATH}1tg6b12000lqisxlaD24A.png`}
                />
              )}
            </>
          )}
        </View>
        {/* 元素占位，初次首屏展示的最慢，抖动撑开元素 */}
        {/* 预订服务提示 */}
        {Util.isCtripIsd() ? (
          <BusinessLicense
            style={{
              marginTop: 0,
              marginBottom: 0,
            }}
          />
        ) : (
          <View className="place-holder" />
        )}
        {/* TODO: 去哪儿小程序屏蔽出境 */}
        {/* {isShowBottomTip && (
          <View className="bottom-tip">
            {tips.map((item, index) => (
              <Block key={item.title}>
                <View
                  className="stip-box"
                  onClick={() => goTipPage(item.jumpUrl)}
                  data-testid={item?.UITestID}
                >
                  <ProcImage
                    src={item.imageUrl}
                    className="stip-img"
                    lazyLoad
                    type={Utils.procImageParamsType.isdHomeSearchPanelTip}
                  />
                  <Text>{item.title}</Text>
                  <Text className="icon-arrow-right" />
                </View>
                {index === 0 && <View className="stip-line" />}
              </Block>
            ))}
          </View>
        )} */}
      </View>

      {/* 预定服务信息弹层 */}
      {isLazyLoad && (
        <BusinessLicenseModal
          visible={isBusinessLicenseModalVisible}
          onClose={handleBusinessLicenseClose}
          title={Texts.businessLicensePageTitle}
        />
      )}

      {/* TODO: 去哪儿小程序屏蔽出境 */}
      {/* {isLazyLoad && isShowNumberAndAge && (
        <>
          <PickerModal
            visible={numberModalVisible}
            selected={adultSelectNum - 1} // 因为成人数量最小值是1，对应selected(index)值为0
            selected2={childSelectNum}
            list={adultRange}
            list2={childRange}
            unit="成人"
            unit2="儿童"
            title="人数"
            confirmCallback={onConfirmNum}
            cancelCallback={() => onPressNumber(false)}
            valueChangeCallback={onNumberChange}
            onPickChangeCallBack={onPickChangeCallBack}
            pickerClass="picker-inset"
          />
          <PickerModal
            visible={ageModalVisible}
            selected={ageIndex}
            list={ageData.list}
            title="驾驶员年龄"
            activeClass="age-active"
            indicatorClass="age-indicator"
            confirmCallback={onConfirmAge}
            cancelCallback={() => onPressAge(false)}
            onPickChangeCallBack={onPickChangeCallBack}
          />
          <Modal
            isMask
            visible={ageIconModalVisible}
            onModalClose={() => onPressAgeIcon(false)}
            hasContent
            renderHeader={
              <Text className="age-title" data-testid={UITestID.car_testid_page_home_why_age}>
                为什么要填写驾驶员年龄
              </Text>
            }
            renderContent={
              <View className="age-tips">
                填写驾驶员年龄可以确认准驾车型，以及是否需要支付青年驾驶费或者高龄驾驶费等额外费用，详情以车行为准。
              </View>
            }
          />
        </>
      )} */}
    </>
  );
};

export default memo(SearchPanel);
