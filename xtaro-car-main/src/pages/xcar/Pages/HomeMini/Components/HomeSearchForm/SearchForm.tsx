import React, { useCallback, useState, memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import debounce from 'lodash/debounce';
import { IPropsType } from './Types';
// import { ToastLoading, Utils } from '../../../utils/index';
// import { isPtimeExpired, getExpiredNewDate } from '../../../state/locationDate/helpers';
// import { getCarContext } from '../../../context/context';
import CarLog from '../../../../Util/CarLog';
import { PickType } from '../../../../Diff/constants';
import SearchPanel from '../HomeSearchPanel/Index';
import Utils from '../../../../Util/Utils';
// import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
// import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

const { PICKUP, DROPOFF } = PickType;

const HomeSearchForm: React.FC<IPropsType> = ({
  ptime,
  rtime,
  setDateInfo,
  onPressSearch,
  isLazyLoad,
  onPickChangeCallBack,
  isCustomizationHome,
  isHomeSearchPanel,
}) => {
  // const context = getCarContext();
  const [role, setRole] = useState(PICKUP);
  const [visible, setVisible] = useState(false);

  // const handleTimepassed = useCallback(
  //   (_ptime, _rtime) => {
  // const curMinutes =
  //   Math.ceil(parseInt(dayjs().format('mm'), 10) / 15) * 15;
  // const timeDiff =
  //   Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
  // const newRentalDate = {
  //   pickup: dayjs().add(4, 'hours').minute(curMinutes),
  //   dropoff: dayjs()
  //     .add(4, 'hours')
  //     .minute(curMinutes + timeDiff),
  // };
  // setDateInfo(newRentalDate);
  // Toast?.show('取车时间已过当前时间，已为您修改取车时间', 2);
  //   },
  //   [setDateInfo],
  // );

  const onDateClick = useCallback(type => {
    const currentRole = type === PICKUP ? PICKUP : DROPOFF;
    setRole(currentRole);
    setVisible(true);
    CarLog.LogCode({
      enName:
        type === PICKUP
          ? 'C_HOME_CHANGEINFO_POP_PICKUP_DATE'
          : 'C_HOME_CHANGEINFO_POP_DROPOFF_DATE',
      name:
        type === PICKUP
          ? '点击_首页_修改取还车信息_修改取车时间'
          : '点击_首页_修改取还车信息_修改还车时间',
    });
  }, []);

  // const onCancel = useCallback(() => {
  //   setVisible(false);
  // }, []);

  const onHandleSearch = useCallback(() => {
    debounce(
      () => {
        onPressSearch();
      },
      300,
      {
        trailing: false,
        leading: true,
      },
    )();
  }, [onPressSearch]);

  return (
    <View
      className="home-page-search"
      // data-testid={UITestID.car_testid_page_home_page_search}
    >
      <SearchPanel
        onPressSearch={onHandleSearch}
        onDateClick={onDateClick}
        isShowFreeDeposit={Utils.isCtripIsd()}
        isShowPickupDoor={Utils.isCtripIsd()}
        isShowNumberAndAge={Utils.isCtripOsd()}
        isShowBottomTip={Utils.isCtripOsd()}
        isLazyLoad={isLazyLoad}
        onPickChangeCallBack={onPickChangeCallBack}
        isCustomizationHome={isCustomizationHome}
        marketThemeEnabled={true}
        isHomeSearchPanel={isHomeSearchPanel}
        showSubsidyImage={true}
      />
    </View>
  );
};

export default memo(HomeSearchForm);
