import Image from '@c2x/components/Image';
import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import MiniChannel from '@miniChannel';
import c2xStyle from './labelC2xStyle.module.scss';

import {
  label,
  font,
  color,
  border,
  icon as iconToken,
  space,
  tokenType,
  radius,
} from '../../../../Tokens';
import { withTheme } from '../../../../Theming';
import BbkChannel, { BbkUtils } from '../../../../Utils';
import BbkText from '../../Text';
import { labelTheme } from './Theme';

const { typeOf, getPixel, autoProtocol, getLineHeight, isAndroid } = BbkUtils;
export const DIMG04_PATH = 'https://dimg04.c-ctrip.com/images/';
const newSplit = `${DIMG04_PATH}1tg0p12000d2ql7hl0058.png`;
const newGraySplit = `${DIMG04_PATH}1tg1j12000d2ql1esF809.png`;

export interface Props extends tokenType.labelProps, tokenType.ColorProps {
  text: string;
  hasBorder?: boolean;
  isFixSmallFontStyle?: boolean;
  /**
   * 无底色
   */
  noBg?: boolean;
  theme?: labelTheme;
  labelStyle?: CSSProperties;
  textStyle?: CSSProperties;
  icon?: IconProps;
  /**
   * 砍价类型标签的前缀
   */
  prefix?: string;
  prefixWrapStyle?: CSSProperties;
  prefixStyle?: CSSProperties;
  prefixImage?: string;
  prefixImageStyle?: CSSProperties;
  // 后缀
  postfix?: string;
  postfixWrapStyle?: CSSProperties;
  postfixStyle?: CSSProperties;
  isSerration?: boolean; // 是否有锯齿边样式
  isEasyLife?: boolean; // 是否是无忧租图片icon
  serrationStyle?: any;
  borColor?: string;
  isSoldOut?: boolean;
  // 虚线样式
  isNewDash?: boolean;
  testID?: string;
}

interface IconProps {
  iconType: 'primary' | 'secondary';
  iconContent: string;
  iconStyle?: CSSProperties;
  iconWrapStyle?: CSSProperties;
  isPrefix?: boolean;
}
const style = StyleSheet.create({
  borderLabelWrap: {
    borderWidth: border.borderSizeXsm,
    borderRadius: radius.radiusXS,
    overflow: 'hidden',
  },
  icon: { fontFamily: iconToken.getIconFamily(), marginRight: space.spaceS },
  noBg: { backgroundColor: color.transparent },
});

const getThemeColor = (theme, colorType) => {
  theme = theme || {};
  if (theme.labelBgColor || theme.labelColor) {
    return theme;
  }
  return theme[colorType] || theme[tokenType.ColorType.Blue] || {};
};

/**
 * style merge rules:
 *
 * 1. colorType              =>  labelBgColor, labelColor
 *    labelSize, labelType   =>  labelStyle, textStyle
 * 2. theme
 * 3. labelStyle, textStyle
 */
const getLabelStyle = ({
  theme,
  hasBorder,
  noBg,
  labelSize,
  labelType,
  colorType = tokenType.ColorType.Blue,
  borColor,
  icon = {} as any,
  isSerration,
}: Props) => {
  const { iconType, iconContent } = icon;
  const isPrimary = iconType === 'primary';

  const colorTheme = getThemeColor(theme, colorType);
  const bgColor = `${colorType || 'blue'}Bg`;
  const baseColor = `${colorType || 'blue'}Base`;
  const borderColor = `${colorType || 'blue'}Border`;

  const {
    labelBgColor = color[hasBorder ? bgColor : baseColor],
    labelColor = color[hasBorder || noBg ? baseColor : bgColor],
    labelBorderColor = color[borderColor] || labelColor,
  } = colorTheme;

  if (iconContent && !labelSize) {
    labelSize = 'L';
  }
  const $labelStyle =
    label[`${labelType}Label${labelSize}Style`] ||
    label[`${labelType}LabelSStyle`] ||
    label[`baseLabel${labelSize}Style`] ||
    label.baseLabelSStyle;

  const labelAddHeight = hasBorder ? style.borderLabelWrap.borderWidth * 2 : 0;

  const $textStyle = font[`label${labelSize}Style`] || font.labelSStyle;

  const themeBgStyle = {
    backgroundColor: labelBgColor,
  };

  return {
    $labelStyle: xMergeStyles([
      $labelStyle,
      {
        minHeight: labelAddHeight + $labelStyle.minHeight,
      },
      !isPrimary && themeBgStyle,
      // #ifdef mini
      hasBorder && {
        position: 'relative',
        border: 'none',
      },
      // #endif
      // #ifndef mini
      hasBorder && {
        ...style.borderLabelWrap,
        borderColor: labelBorderColor,
      },
      // #endif
      noBg && style.noBg,
    ]),

    $textStyle: xMergeStyles([
      $textStyle,
      {
        color: labelColor,
        flexWrap: 'wrap',
      },
    ]),

    $iconStyle: xMergeStyles([
      style.icon,
      {
        width: label.baseLabelLHeight,
        height: label.baseLabelLHeight,
        lineHeight: getLineHeight(32),
        textAlign: 'center',
        borderRadius: radius.radiusXS,
        color: labelColor,
        fontSize: $textStyle.fontSize,
      },
      isPrimary && themeBgStyle,
    ]),

    $prefixWrapStyle: xMergeStyles([
      $labelStyle,
      {
        backgroundColor: labelColor,
        marginLeft: -$labelStyle.paddingLeft,
        marginRight: $labelStyle.paddingLeft,
      },
    ]),

    $postfixWrapStyle: xMergeStyles([
      {
        backgroundColor: isSerration ? color.labelPostfixBg : labelColor,
        marginRight: -$labelStyle.paddingLeft,
        marginLeft: getPixel(4),
        paddingLeft: getPixel(4),
        paddingRight: getPixel(8),
      },
    ]),

    $postfixStyle: {
      ...$textStyle,
      color: color.white,
    },
    $prefixStyle: {
      ...$textStyle,
      color: color.white,
    },
    $serrationLeft: {
      borderColor: borColor,
    },
    $serrationRight: {
      borderColor: borColor,
    },
  };
};

const tranformArray2Obj = (input: any) => {
  if (typeOf(input) === 'Array') {
    return input.reduce((m, v) => {
      if (typeOf(v) === 'Array') {
        return tranformArray2Obj(v);
      }
      if (v) {
        return { ...m, ...v };
      }
      return m;
    }, {});
  }
  return input;
};

const fixWebLabel = ($textStyle: any, textStyle: any) => {
  const fixStyle = {
    ...tranformArray2Obj($textStyle),
    ...tranformArray2Obj(textStyle),
  };
  return {};
};

const BbkComponentLabel = (props: Props) => {
  const {
    text,
    hasBorder,
    labelStyle,
    textStyle,
    prefix,
    prefixWrapStyle,
    prefixStyle,
    icon = {},
    prefixImage,
    prefixImageStyle,
    postfix,
    postfixWrapStyle,
    postfixStyle,
    isFixSmallFontStyle = true,
    isSerration,
    serrationStyle,
    isEasyLife,
    isSoldOut,
    isNewDash = false,
    testID,
  } = props;
  const {
    iconType,
    iconContent,
    iconStyle,
    isPrefix = true,
    iconWrapStyle,
    iconVersion,
  } = icon as any;
  const {
    $labelStyle,
    $textStyle,
    $iconStyle,
    $prefixWrapStyle,
    $prefixStyle,
    $postfixWrapStyle,
    $postfixStyle,
    $serrationLeft,
    $serrationRight,
  } = getLabelStyle(props) as any;
  const iconDom = iconContent && (
    <View style={iconWrapStyle}>
      <BbkText
        type="icon"
        iconVersion={iconVersion}
        style={xMergeStyles([$iconStyle, iconStyle])}
      >
        {iconContent}
      </BbkText>
    </View>
  );

  const splitUrl = autoProtocol(
    `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/dashed${
      isSoldOut ? '_gray' : ''
    }.png`,
  );
  const newSplitUrl = isSoldOut ? newGraySplit : newSplit;

  // 小程序差异化: 增加小程序1px边框兼容
  const getMergedStyle = () => {
    const baseStyle = xMergeStyles([$labelStyle, labelStyle]);

    if (!MiniChannel.isMini() || !hasBorder) {
      return baseStyle;
    }

    // 用户自定义样式 > 默认样式 > 兜底默认值
    return {
      ...baseStyle,
      ['--border-width']: `${labelStyle?.borderWidth ?? $labelStyle?.borderWidth ?? 1}px`,
      ['--border-color']:
        labelStyle?.borderColor ?? $labelStyle?.borderColor ?? 'transparent',
      ['--border-radius']: `${labelStyle?.borderRadius ?? $labelStyle?.borderRadius ?? 0}px`,
    } as React.CSSProperties;
  };

  return (
    <View testID={testID}>
      <View
        className={MiniChannel.isMini() && hasBorder ? c2xStyle.miniBorder : ''}
        // 小程序差异化: 增加小程序1px边框兼容
        style={getMergedStyle()}
      >
        {!!prefixImage && (
          <Image
            resizeMode={isEasyLife ? 'cover' : 'contain'}
            src={prefixImage}
            style={prefixImageStyle}
          />
        )}
        {isPrefix && iconDom}
        {!!prefix && (
          <View style={xMergeStyles([$prefixWrapStyle, prefixWrapStyle])}>
            <BbkText style={xMergeStyles([$prefixStyle, prefixStyle])}>
              {prefix}
            </BbkText>
          </View>
        )}
        <BbkText
          smallFontStyle={
            isFixSmallFontStyle ? fixWebLabel($textStyle, textStyle) : {}
          }
          numberOfLines={100}
          style={xMergeStyles([
            $textStyle,
            textStyle,
            // 小程序业务差异化： 样式微调
            isAndroid
              ? { transform: `translateY(${getPixel(2)}px)` }
              : { transform: `translateY(${getPixel(-1)}px)` },
          ])}
        >
          {text}
        </BbkText>
        {!!postfix && (
          <View>
            <Image
              src={isNewDash ? newSplitUrl : splitUrl}
              mode="aspectFit"
              className={isNewDash ? c2xStyle.dashedNew : c2xStyle.dashed}
            />

            <View style={xMergeStyles([$postfixWrapStyle, postfixWrapStyle])}>
              <BbkText
                style={xMergeStyles([
                  // 小程序业务差异化： 样式微调
                  isAndroid
                    ? { transform: `translateY(${getPixel(2)}px)` }
                    : { transform: `translateY(${getPixel(1)}px)` },
                  $postfixStyle,
                  postfixStyle,
                ])}
              >
                {postfix}
              </BbkText>
            </View>
          </View>
        )}
        {!isPrefix && iconDom}
      </View>
      {isSerration && (
        <Image
          src={autoProtocol(
            `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/market_tag_left_half_circle${
              isSoldOut ? '_gray' : ''
            }.png`,
          )}
          mode="aspectFit"
          className={c2xStyle.serrationLeft}
          style={xMergeStyles([$serrationLeft, serrationStyle])}
        />
      )}
      {isSerration && (
        <Image
          src={autoProtocol(
            `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/market_tag_right_half_circle${
              isSoldOut ? '_gray' : ''
            }.png`,
          )}
          mode="aspectFit"
          className={c2xStyle.serrationRight}
          style={xMergeStyles([$serrationRight, serrationStyle])}
        />
      )}
    </View>
  );
};

export default withTheme(BbkComponentLabel);
