import { get as lodashGet } from 'lodash-es';
/* eslint-disable @typescript-eslint/default-param-last */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { createSelector } from 'reselect';
import MiniChannel from '@miniChannel';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import {
  VendorPriceListType,
  ReferenceType,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { IInputType } from '../../ComponentBusiness/BookForm/src/Type';
import {
  getBaseResData,
  getPriceResData,
  getProductReq,
  getProductRequestId,
  getReferenceTemp,
  getBookingFirstScreenParam,
  getVendorInfo,
  getQueryProductInfoStoreGuidInfos,
  getSesameInfo,
  getDepositInfo,
  getDepositRateDescriptionContent,
} from '../../Global/Cache/ProductSelectors';
import {
  getPickUpTime,
  getDropOffTime,
  getProductDropOffTime,
  getPickUpLocationName,
  getDropOffLocationName,
} from '../LocationAndDate/Selectors';
import {
  getDepositPayType,
  getCurInsPackageId,
  getSelectedIdType,
  getIsShowTravelLimit,
  getTravelLimitSelectedResult,
  getCurPackageId,
} from '../Product/Selectors';
import { PickUpMaterials } from '../../Types/Dto/DetailType';
import {
  getCurPriceInfo,
  getCurCtripInsuranceIds,
  getCrossPlacesMap,
  getRegionIds,
  getCurPackage,
  getCurProductInfo,
  getCurPackageIsEasyLife,
  getIsKlb,
} from '../Product/Mappers';
import {
  getNationalChainTag,
  mergeVehicleYearLabel,
} from '../List/VehicleListMappers';
import { getCrossLocationsFromCache } from '../Product/Model';
import { Utils, CarABTesting, getOTimeOutInterval } from '../../Util/Index';
import { getPassenger } from '../DriverList/Selectors';
import AppContext from '../../Util/AppContext';
import texts from '../../Pages/Booking/Texts';
import { BuildInsuranceParamsRequestType } from '../../Constants/Types/InsuranceConfirmDtoType';
import {
  getPackageVendorProps,
  isCreditRentPayType,
} from '../Product/BbkMapper';
import { ShowPayModeType, LateDepositVer } from '../Product/Enums';
// #ifndef mini 注释-sesame
import { isCtripCreditRent as isCtripCreditRentFn } from '../Sesame/Mappers';
// #endif
import ServerMapping from '../../Constants/ServerMapping';
import { FooterBarDataType } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { PriceInfoResponseType } from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import {
  getTagListInfo,
  IAllTagsInfoType,
} from '../../Pages/Booking/VehicleAndVendorInfo';
import { getQueryVehicleDetailInfoStoreGuidInfos } from '../ProductConfirm/Selectors';
import { PayType } from '../../Constants/PayEnums';
import { CarPayParams } from '../../Types/PaymentType';
import { verifyCreateOrderPointInfo, verifyDriverInfo } from './Method';
import { ReqInfoType } from '../../Types/Dto/RentalMustReadResponseType';
import { IMergeGuidInfo, IDepositMethodItem } from '../../Pages/Booking/Types';
import { Constants, Enums } from '../../ComponentBusiness/Common';
import { ApiResCode } from '../../Constants/Index';
import {
  DepositPayType,
  MaterialsType,
} from '../../ComponentBusiness/Common/src/Enums';
import { IDepositDescriptionType } from '../../Pages/Product/Types';
import { ILableCode } from '../../Constants/CommonEnums';
import { getRebookParamsOsd } from '../ModifyOrder/CommonSelector';
import { ContactType } from '../../Constants/LocalContactsData';

const CTRIP_PERSONAL_PROPERTY_INSURANCE = [
  ServerMapping.FEE_CODES.CTRIP_PERSONAL_PROPERTY_INSURANCE_OLD,
  ServerMapping.FEE_CODES.CTRIP_PERSONAL_PROPERTY_INSURANCE,
];

const { osdInsUnit } = texts;
export const getResData = () => getBaseResData();
export const getPayMode = state => state.Product.payMode;
export const getPackageId = state => state.Product.curPackageId;
export const getFlightErrorCode = state => state.Booking.flightErrorCode;
const getSelectedExtras = state => state.Product.selectedExtras;
const getEquipmentInfos = state => state.Product.equipmentInfos;
export const getDriverInfo = state => state.Booking.driverInfo;
const getCertificates = state => state.DriverList.curCertificates;
export const getRebookPenalty = state => state.Booking.rebookPenalty;
export const getStrongSubmitInfoReqParams = state =>
  state.Booking.strongSubmitInfoReqParams;
const getAvailableCertificates = state =>
  state.DriverList.availableCertificates;
export const getIsNameReverse = state => state.Booking.isNameReverse;

export const getOrderData = state => state.Booking.orderData;
export const getPreLicensData = state => state.Booking.preLicensData;
export const getSelectedCouponsCodes = state =>
  state.Booking.selectedCouponsCodes;
export const getUniqueOrderModalVisible = state =>
  state.Booking.uniqueOrderModalVisible;
export const getCreateOrderFailModalVisible = state =>
  state.Booking.createOrderFailModalVisible;
export const getFlightErrorModalVisible = state =>
  state.Booking.flightErrorModalVisible;
export const getSupplierModalVisible = state =>
  state.Booking.supplierModalVisible;
export const getDepositIntroduceModalVisible = state =>
  state.Booking.depositIntroduceModalVisible;
export const getCtripCreditFModalVisible = state =>
  state.Booking.ctripCreditFModalVisible;
export const getPriceLoading = state => state.Product.isPriceLoading;
export const getPriceTimerLoading = state => state.Product.isPriceTimerLoading;
export const getPassengerError = state => state.Booking.passengerError;
export const getPrice = () => getPriceResData();
const getCrossPlaces = state => state.Product.crossPlaces;
// #ifndef mini 18631/AddSnapShot未使用redux注释
export const getIsSnapShotFinish = state => state.Booking.isSnapShotFinish;
// #endif
export const getPriceChangePopVisible = state =>
  state.Booking.priceChangePopVisible;

export const getCreateInsLoadingPopVisible = state =>
  state.Booking.createInsLoadingPopVisible;
export const getCreateInsFailPopVisible = state =>
  state.Booking.createInsFailPopVisible;
export const getEasyLifePopVisible = state =>
  state.Booking.isEasyLifeModalVisible;
export const getEhiFreeDepositModalVisible = state =>
  state.Booking.ehiFreeDepositModalVisible;
export const getFreeDepositModalVisible = state =>
  state.Booking.freeDepositModalVisible;
export const getIousInfo = state => state.Product.iousInfo;
export const isNaquhuaValidated = state =>
  state.Product.iousInfo?.instalmentDetailList?.length > 0;
export const getSelectedLoanPayStageCount = state =>
  state.Product.selectedLoanPayStageCount;
export const getLogInfo = state => state.Booking.logInfo;

export const getVendorPriceInfo = state => state.Booking.vendorPriceInfo;

export const getIsBusinessLicenseModalVisible = state =>
  state.Booking.isBusinessLicenseModalVisible;

export const getFuelDescriptionModalVisible = state =>
  state.Booking.isFuelDescriptionModalVisible;

// 获取出境修改订单提示
export const getOsdModifyOrderNote = state => state.Booking.osdModifyOrderNote;

export const getBookingLogParams = createSelector(
  [getVendorPriceInfo],
  vendorPriceInfo => ({
    vehicleCode: vendorPriceInfo?.ctripVehicleCode,
    storeCode: vendorPriceInfo?.reference?.pStoreCode,
    rStoreCode: vendorPriceInfo?.reference?.rStoreCode,
  }),
);

export const getIsEasyLife2024 = createSelector(
  [getVendorPriceInfo],
  vendorPriceInfo =>
    vendorPriceInfo?.reference?.packageLevel === ApiResCode.EasyLife2024Code,
);

export const getAdditionalDriverDesc = createSelector(
  [getResData],
  productInfo => {
    const data = productInfo?.promptInfos?.find(
      v => v.type === ApiResCode.ListPromptType.AdditionalDriver,
    );
    return {
      title: data?.title,
      content: data?.contents?.[0]?.stringObjs?.[0].content,
    };
  },
);

export const getCheckFlightNoLoading = state =>
  state.Booking.checkFlightNoLoading;

export const getFlightErrorTip = state => state.Booking.flightErrorTip;

export const getDepositRateDescriptionModalVisible = state =>
  state.Booking.depositRateDescriptionModalVisible;

export const getFlightDelayRulesModalVisible = state =>
  state.Booking.flightDelayRulesModalVisible;

export const getUniqueReference = createSelector<any, any, ReferenceType>(
  [getVendorPriceInfo],
  priceInfo => {
    return priceInfo?.reference;
  },
);

export const getPayRequestId = createSelector(
  [getPreLicensData],
  preLicensData => lodashGet(preLicensData, 'payRequestId'),
);

export const getOrderPrice = createSelector(
  [getPrice],
  priceInfo => priceInfo && priceInfo.orderPriceInfo,
);

const getFeeDetailInfo = createSelector(
  [getPrice],
  priceInfo => priceInfo && priceInfo.feeDetailInfo,
);
const getSelectedCoupon = createSelector(
  [getPrice],
  priceInfo =>
    priceInfo && priceInfo.couponList && priceInfo.couponList.selectedCoupon,
);
export const getDriverFeeDesc = createSelector(
  [getCurPriceInfo, getPassenger],
  (curPriceInfo, passenger) => {
    const age = passenger?.age;
    if (!curPriceInfo || !age) return '';
    const { ageRestriction = {} } = curPriceInfo;
    let driverFeeDesc = '';
    const {
      youngDriverAgeDesc,
      oldDriverAgeDesc,
      youngDriverAge,
      oldDriverAge,
    } = ageRestriction;
    if (youngDriverAge && youngDriverAge > 0 && age <= youngDriverAge) {
      driverFeeDesc = youngDriverAgeDesc;
    } else if (oldDriverAge && oldDriverAge > 0 && age >= oldDriverAge) {
      driverFeeDesc = oldDriverAgeDesc;
    }
    return driverFeeDesc;
  },
);
const getLocalPhoneNumber = v => {
  const { contactType, contactTypeName, value } = v || {};
  if (contactType === ContactType.localPhone) {
    return {
      contactType,
      contactInfo: `${contactTypeName} ${value}`,
    };
  }
  return {
    contactType,
    contactInfo: value,
  };
};
export const getDriversMap = createSelector(
  [
    getDriverInfo,
    getIsNameReverse,
    getPassenger,
    getCertificates,
    getAvailableCertificates,
    getSelectedIdType,
    getIsKlb,
  ],

  (
    driverInfo,
    isNameReverse,
    passenger,
    certificates = [],
    availableCertificates = [],
    selectedIdType = null,
    isKlb,
  ) => {
    if (!driverInfo || !driverInfo.length) return {};
    const drivers = driverInfo.reduce((m, v) => {
      switch (v.type) {
        case IInputType.firstName:
          return { ...m, firstName: v.value.trim() };
        case IInputType.lastName:
          return { ...m, secondName: v.value.trim() };
        case IInputType.email:
          return { ...m, email: v.value };
        case IInputType.mobilePhone:
          return {
            ...m,
            cellPhone:
              // 小程序业务差异化：去哪儿手机号按掩码格式返回，无需正则过滤字符
              MiniChannel.isQunar() && v.value
                ? v.value
                : v.value.replace(/[^0-9]+/g, ''),
          };
        case IInputType.flightNumber:
          return { ...m, flightNo: v.value };
        case IInputType.areaCode:
          return { ...m, areaCode: v.value || '86' };
        case IInputType.wechat:
          return { ...m, weChat: v.value };
        case IInputType.driverLicense:
          return { ...m, driverLicense: v.value };
        case IInputType.driverLicenseName:
          return { ...m, driverLicenseName: v.value };
        case IInputType.localContacts:
          return {
            ...m,
            ...getLocalPhoneNumber(v),
          };
        default:
          return m;
      }
    }, {});
    const { firstName, secondName } = drivers;
    const isKarabi = Utils.isCtripOsd() && isKlb;
    if (!isKarabi) {
      drivers.secondName = firstName;
      drivers.firstName = secondName;
    }
    let name = isNameReverse
      ? `${drivers.secondName} ${drivers.firstName}`
      : `${drivers.firstName} ${drivers.secondName}`;
    let birthday = '';
    if (passenger) {
      drivers.secondName = isKarabi ? passenger.lastName : passenger.firstName;
      drivers.firstName = isKarabi ? passenger.firstName : passenger.lastName;
      drivers.driverCountryName = passenger.nationalityName;
      drivers.driverCountryCode = passenger.nationality;
      if (Utils.isCtripIsd()) {
        name = passenger.fullName;
        // 填写页的下单驾驶员除了跟上次编辑的驾驶员有关，还与供应商支持的驾驶员有关
        const { certificateList = [], passengerId = '' } = passenger;
        const currType = certificates[passengerId];

        let certificateObj;
        certificateObj = certificateList.find(citem =>
          availableCertificates.includes(citem.certificateType),
        );
        if (availableCertificates.includes(currType)) {
          certificateObj = certificateList.find(
            item => item.certificateType === currType,
          );
        }
        if (certificateObj) {
          const { certificateType, certificateNo } = certificateObj;
          drivers.idtype = certificateType;
          drivers.idnumber = certificateNo;
        }
      } else {
        name =
          drivers.firstName && drivers.secondName
            ? `${drivers.firstName} ${drivers.secondName}`
            : drivers.firstName || drivers.secondName;
        birthday = passenger?.birthday;
      }
    }
    const age = passenger && passenger.age ? passenger.age : 0;
    return {
      ...drivers,
      name,
      birthday,
      age,
      countryCode: selectedIdType?.idtype || '',
    };
  },
);
export const getCurrentPriceInfo = createSelector(
  [getPrice],
  priceInfo =>
    (priceInfo &&
      priceInfo.payModeInfos &&
      priceInfo.payModeInfos.find(v => v.isSelected)) ||
    {},
);

export const getPayModeInfos = createSelector(
  [getPrice],
  priceInfo => (priceInfo && priceInfo.payModeInfos) || [],
);

export const getDepositPayInfos = createSelector(
  [getPrice],
  priceInfo => (priceInfo && priceInfo.depositPayInfos) || [],
);

// #ifdef mini 添加小程序免押所需参数
export const getProductDepositInfo = createSelector(
  [getPrice],
  priceInfo => (priceInfo && priceInfo.depositPayInfos) || [],
);
// #endif
// #ifdef mini 添加小程序免押所需参数
export const getPromptInfos = createSelector(
  [getPrice],
  priceInfo => (priceInfo && priceInfo.promptInfos) || [],
);
// #endif
export const getCertInstructions = createSelector([getPrice], priceInfo => {
  const strings = priceInfo?.certInstructions?.stringObjs || [];
  return strings.map(item => item.content);
});

export const getSelectInsuranceIds = createSelector(
  [getPrice, getCurInsPackageId],
  (priceInfo, curInsPackageId) => {
    const addOnServices = Utils.isCtripIsd()
      ? lodashGet(priceInfo, 'addOn.addOnServices')
      : null;
    const insuranceId = addOnServices
      ? addOnServices
          .filter(v => CTRIP_PERSONAL_PROPERTY_INSURANCE.includes(v.uniqueCode))
          .map(v => v.uniqueCode)
      : getCurCtripInsuranceIds(curInsPackageId);
    return insuranceId;
  },
);

export const getCtripRentNeedValidateOrder = createSelector(
  [getDepositPayType],
  depositPayType =>
    CarABTesting.isCreditRent() && isCreditRentPayType(depositPayType),
);

export const getNeedValidateOrder = createSelector(
  [getCtripRentNeedValidateOrder, getCurrentPriceInfo],
  (ctripRentNeedValidateOrder, priceInfo) => {
    const needValidateOrder =
      priceInfo?.showPayMode === ShowPayModeType.Auth ||
      ctripRentNeedValidateOrder;

    return needValidateOrder;
  },
);

const getLocationInfo = (storeInfo, hub) => ({
  storeType: storeInfo.storeType,
  hub,
  storeAddress: storeInfo?.address,
  storeName: storeInfo?.storeName,
  workTime: storeInfo?.workTime,
  shuttlePointAddress: storeInfo?.shuttlePointAddress,
  shuttlePointName: storeInfo?.shuttlePointName,
  shuttlePointWorkTime: storeInfo?.shuttlePointWorkTime,
});

export const getResCancelFeeRebook = state => state.Booking.resCancelFeeRebook;

export const getIsSelectedPackageValid = createSelector(
  [getPackageId, getSelectedExtras, getEquipmentInfos],
  (packageId, selectedExtra, equipmentInfos) => {
    const curPackageEquipments = equipmentInfos?.find(
      v => v.packageId === packageId,
    )?.equipments;
    if (selectedExtra?.length && !curPackageEquipments?.length) {
      // 当前选中的套餐没有可选的设备
      return false;
    }
    return true;
  },
);

export const getOrderParams = createSelector(
  [
    getResData,
    getPrice,
    getProductReq,
    getPackageId,
    getDriversMap,
    getSelectedExtras,
    getSelectedCoupon,
    getCurrentPriceInfo,
    getCrossPlaces,
    getCurPriceInfo,
    getPayRequestId,
    getPickUpLocationName,
    getDropOffLocationName,
    getDepositPayType,
    getCurInsPackageId,
    getPreLicensData,
    getNeedValidateOrder,
    getRebookPenalty,
    getStrongSubmitInfoReqParams,
    getOrderPrice,
    getIsShowTravelLimit,
    getTravelLimitSelectedResult,
    getRebookParamsOsd,
  ],

  (
    productInfo,
    priceInfo,
    productParams,
    packageId,
    driver,
    extras,
    couponInfo,
    currentPriceInfo,
    crossPlace,
    curPriceInfo,
    payRequestId,
    pickUpLocationName,
    dropOffLocationName,
    depositPayType,
    curInsPackageId,
    preLicensData,
    needValidateOrder,
    rebookPenalty,
    strongSubmitInfoReqParams,
    orderPriceInfo = {},
    isShowTravelLimit,
    travelLimitSelectedResult,
    rebookParamsOsd,
  ) => {
    const {
      pickupStoreInfo = {},
      returnStoreInfo = {},
      vendorInfo = {},
      vehicleInfo = {},
      extra = {},
    } = productInfo;
    const dailyPriceInfo = lodashGet(priceInfo, 'dailyPriceInfo');
    const activityInfo =
      priceInfo &&
      priceInfo.activityDetail &&
      priceInfo.activityDetail.promotion;
    const { packagePrice, totalPrice, payAmount, dailyPrice } = orderPriceInfo;
    const {
      pickupPointInfo: pickupPoint = {},
      returnPointInfo: returnPoint = {},
    } = productParams;
    const loactionRes = getCrossLocationsFromCache(curInsPackageId);
    const { crossLocationInfos, commmonPolicies, selectedCrossIslands } =
      getCrossPlacesMap(crossPlace, loactionRes);

    const coupList = [];
    if (couponInfo && couponInfo.code) {
      coupList.push({
        cCode: couponInfo.code,
        unionType: couponInfo.unionType,
        vendorCouponCode: couponInfo.vendorCouponCode,
        vendorKey: couponInfo.vendorKey,
        couponType: couponInfo.couponType,
      });
    }
    // 活动参数
    const activities = [];
    const { promotions } = priceInfo?.activityDetail || {};
    if (Utils.isCtripIsd() && promotions?.length) {
      promotions.forEach(item => {
        activities.push({
          actCode: item.code,
          actId: item.promotionId,
          discountType: item.discountType,
        });
      });
    } else {
      // 统一校验逻辑：境外需code存在，非境外只需activityInfo存在
      const shouldPush = Utils.isCtripOsd()
        ? activityInfo && activityInfo.code
        : activityInfo;

      if (shouldPush) {
        activities.push({
          actCode: activityInfo.code,
          actId: activityInfo.promotionId,
          discountType: activityInfo.discountType,
        });
      }
    }

    // 境外保险参数
    const { subType, packageName } = getCurPackage(curInsPackageId);

    const addOnServices = Utils.isCtripIsd()
      ? lodashGet(priceInfo, 'addOn.addOnServices')
      : null;

    // 下单时使用填写页返回的priceCode

    const priceCode = lodashGet(priceInfo, 'orderPriceInfo.priceCode');
    const referenceTemp = getReferenceTemp();
    const reference = {
      ...productParams.reference,
      priceCode,
      ...referenceTemp,
    };
    const { name, vehicleCode, groupName, groupCode } = vehicleInfo;
    const { vendorCode, vendorName, bizVendorCode } = vendorInfo;
    const { isEasyLife, pStoreCode, rStoreCode, pHub, rHub } = reference;
    const pickupPointInfo = {
      ...pickupPoint,
      datetime: pickupPoint.date,
      storeCode: pickupStoreInfo.storeCode,
      address: pickUpLocationName,
    };
    const returnPointInfo = {
      ...returnPoint,
      datetime: returnPoint.date,
      storeCode: returnStoreInfo.storeCode,
      address: dropOffLocationName,
    };
    const verifyRequestParameters = () => {
      return (
        verifyCreateOrderPointInfo(pickupPointInfo) &&
        verifyCreateOrderPointInfo(returnPointInfo) &&
        verifyDriverInfo(driver)
      );
    };

    // 验单逻辑
    // 1.验单返回requestId
    // 2.跳转支付传递requestId
    // 3.下单传递 payRequestId = requestId
    const reqInfo = {
      basic: {
        needCheckFlight: true,
        // 只有需要验单的情况下传 payRequestId
        payRequestId: needValidateOrder ? payRequestId : '',
        // 验单 tempOrderId
        orderId: lodashGet(preLicensData, 'tempOrderId'),
        modifyOrderId: AppContext.originOrderId,
        modifyVendorOrderCode: AppContext.modifyVendorOrderCode,
        originVendorId: Number(AppContext.originVendorId) || 0,
        applyRefundLossFee: Number(rebookPenalty) || 0,
        oriCtripOrderId: rebookParamsOsd?.ctripOrderId,
      },
      condition: {
        pickupPointInfo,
        returnPointInfo,
      },
      product: {
        reference,
        subType,
        packageId,
        packageName,
        vehicleCode,
        vehicleName: name,
        groupName,
        bizVendorCode,
        pStoreCode,
        rStoreCode,
        vendorCode,
        vendorName,
        isEasyLife,
        groupCode,
        pickUpLocation: getLocationInfo(pickupStoreInfo, pHub),
        returnLocation: getLocationInfo(returnStoreInfo, rHub),
      },
      customerInfo: {
        driver,
        crossLocationBunchInfo: {
          crossLocationInfos,
          commmonPolicies,
        },
        selectedCrossIslands,
      },
      insurance: {
        insuranceIds: addOnServices
          ? addOnServices
              .filter(v =>
                CTRIP_PERSONAL_PROPERTY_INSURANCE.includes(v.uniqueCode),
              )
              .map(v => v.uniqueCode)
          : getCurCtripInsuranceIds(curInsPackageId),
        packageIdforadditionalinsuarnce: 0,
      },
      addOn: {
        addOnServices: Utils.isCtripIsd()
          ? addOnServices
          : extras &&
            extras.map(v => ({
              equipcode: v.equipmentCode,
              count: v.currentNum,
            })),
      },
      orderPayment: {
        coupList,
        activities,
        payAmount,
        totalPrice,
        dailyPrice,
        packagePrice,
        payMode: currentPriceInfo && currentPriceInfo.payMode,
        payInfo: {
          freepreccancy: false,
          preauth: vendorInfo && vendorInfo.supportPreAuth,
          pytp: currentPriceInfo && currentPriceInfo.payType,
        },
        pricecode: priceCode,
        depositPayType: payAmount === 0 ? DepositPayType.Store : depositPayType,
        freeDepositWay: lodashGet(priceInfo, 'depositLabel.type'),
      },
      dailyPriceInfo,
      extraMaps: extra,
      appRequestMap: {
        verifyRequestParameters,
      },
      strongSubmitInfo: strongSubmitInfoReqParams?.strongSubmitInfo,
      regionIds: isShowTravelLimit
        ? getRegionIds(travelLimitSelectedResult)
        : null,
    };
    return reqInfo;
  },
);

const getMiddlePayVendorId = () => {
  const vendorInfo = getVendorInfo();
  return Utils.isCtripOsd()
    ? vendorInfo?.bizVendorCode
    : vendorInfo?.vendorCode;
};

export const getPaymentParams = createSelector(
  [
    getResData,
    getPrice,
    getFeeDetailInfo,
    getCurrentPriceInfo,
    getPickUpTime,
    getProductDropOffTime,
    getPickUpLocationName,
    getDropOffLocationName,
    getDriversMap,
    getOrderData,
    getDepositPayType,
    getProductRequestId,
    getPreLicensData,
    getIousInfo,
    getSelectedLoanPayStageCount,
    getIsKlb,
  ],

  (
    productInfo,
    priceInfo,
    feeDetail,
    currentPriceInfo,
    ptime,
    rtime,
    pickupLocation,
    returnLocation,
    driver,
    orderInfo,
    depositPayType,
    requestId,
    preLicensData,
    iousInfo,
    selectedLoanPayStageCount,
    isKlb,
  ): CarPayParams => {
    const {
      orderId,
      payAmount,
      payAmountInfo,
      lastEnablePayTime,
      leftMinutes,
      leftSeconds,
      payToken,
      isNew,
      payLink,
      merchantId,
      payRequestId,
    } = orderInfo;
    const { tempOrderId } = preLicensData || {};
    const { currenctPriceInfo } = currentPriceInfo;
    const { vehicleInfo = {}, vendorInfo = {} } = productInfo;
    const isFreeCancel = !!(
      priceInfo &&
      priceInfo.cancelRuleInfo &&
      priceInfo.cancelRuleInfo.showFree
    );

    // let extras = priceInfo && priceInfo.feeDetail.equipmentInfos && priceInfo.equipmentInfos;
    // if (!extras) extras = [];
    const promotions = [];
    if (feeDetail && feeDetail.activityInfo) {
      const { title, currencyCode, currentTotalPrice } = feeDetail.activityInfo;
      promotions.push({
        title,
        currencyCode,
        currentTotalPrice,
        isDiscount: true,
      });
    }
    if (
      feeDetail &&
      feeDetail.couponInfos &&
      feeDetail.couponInfos[0] &&
      feeDetail.couponInfos[0].currentTotalPrice
    ) {
      const { title, currencyCode, currentTotalPrice } =
        feeDetail.couponInfos[0];
      promotions.push({
        title,
        currencyCode,
        currentTotalPrice,
        isDiscount: true,
      });
    }
    let chargesInfos = feeDetail && feeDetail.chargesInfos;
    if (!chargesInfos) {
      chargesInfos = [];
    }
    // 海外过滤费用项没有价格的费用项
    if (Utils.isCtripOsd()) {
      chargesInfos = chargesInfos?.filter(item => !!item.currentTotalPrice);
    }

    // 补充在线支付附加产品
    let extras = [];
    if (feeDetail && feeDetail.equipmentInfos) {
      extras = feeDetail.equipmentInfos;
    }

    // 拿去花参数
    let selectedLoanPayBusType = '';
    const { instalmentDetailList = [] } = iousInfo || {};
    instalmentDetailList.forEach(item => {
      if (item.loanPayStageCount === selectedLoanPayStageCount) {
        selectedLoanPayBusType = item.loanPayBusType;
      }
    });

    const paymentParams = isKlb
      ? {
          payToken,
          isNew,
          payLink,
          merchantId,
          payRequestId,
        }
      : {};

    return {
      // 验单没有 orderId，有 tempOrderId
      orderId: orderId || tempOrderId,
      title: vehicleInfo.name,
      payType: PayType.RegularPay,
      subtitle: vendorInfo.vendorName,
      vendorId: getMiddlePayVendorId(),
      currency: currenctPriceInfo && currenctPriceInfo.currencyCode,
      amount: payAmount,
      isHertzPrepay: currentPriceInfo && currentPriceInfo.packageType,
      freeCancel: isFreeCancel ? '取车前免费取消' : undefined,
      payName:
        currentPriceInfo &&
        currentPriceInfo.payMode &&
        currentPriceInfo.payMode === 3
          ? '预付押金'
          : '在线预付',

      ptime: dayjs(ptime).format('YYYY-MM-DD HH:mm:ss'),
      rtime: dayjs(rtime).format('YYYY-MM-DD HH:mm:ss'),
      pickupLocation,
      returnLocation,
      driver,
      chargesInfos: [...chargesInfos, ...promotions, ...extras],
      payAmountInfo,
      orderTimeOutInterval: getOTimeOutInterval({
        orderBaseInfo: {
          lastEnablePayTime,
        },
        continuePayInfo: {
          leftMinutes,
          leftSeconds,
        },
      }),
      extensions: {
        depositPayType,
        freeDepositWay: lodashGet(priceInfo, 'depositLabel.type'),
      },
      requestId,
      parentRequestId: requestId,
      payExtend: {
        loanPayStageCount: selectedLoanPayStageCount,
        loanPayBusType: selectedLoanPayBusType,
      },
      ...paymentParams,
    };
  },
);

export const getInsConfirmReqParam = createSelector(
  [getDriversMap, getCurInsPackageId, getSelectInsuranceIds],
  (driver, curInsPackageId, selectInsuranceIds) => {
    // @ts-ignore
    const params: BuildInsuranceParamsRequestType = {};
    const insuredInfo = {
      name: driver.name,
      idCardType: driver.idtype,
      idCardNo: driver.idnumber,
      age: driver.age,
      insuredId: AppContext.UserInfo.userId,
    };

    const curProductInfo = getCurProductInfo(curInsPackageId);
    const curAllCtripInsurances =
      lodashGet(curProductInfo, 'ctripInsurances') || [];

    params.insuredList = [insuredInfo];
    params.policyHolder = insuredInfo;

    const selectedInsuranceList = [];
    const insuranceList = [];
    if (selectInsuranceIds && selectInsuranceIds.length > 0) {
      selectInsuranceIds.forEach(item => {
        /* eslint-disable eqeqeq */
        const curIns =
          curAllCtripInsurances.find(f => f.uniqueCode == item) || {};
        const insPrice = Utils.isCtripIsd()
          ? curIns.currentTotalPrice
          : curIns.currentDailyPrice;
        let selectedInsInfo: any = {
          insuranceId: item,
        };
        if (Utils.isCtripIsd()) {
          selectedInsInfo = {
            ...selectedInsInfo,
            insuredId: AppContext.UserInfo.userId,
          };
        }
        selectedInsuranceList.push(selectedInsInfo);
        let priceText = '';
        if (insPrice) {
          priceText = Utils.isCtripIsd()
            ? `¥${insPrice}`
            : `¥${insPrice}${osdInsUnit}`;
        }
        insuranceList.push({
          insuranceId: item,
          priceNoteList: [
            {
              priceText,
            },
          ],
        });
      });
    }
    params.selectedInsuranceList = selectedInsuranceList;
    params.insuranceList = insuranceList;
    params.callbackType = 1; // 回调方式，1: event，2: url跳转，3: 原生小程序跳转

    return params;
  },
);

export const getPriceDetailModalData = createSelector<
  any,
  PriceInfoResponseType,
  any
>([getPrice], priceInfo => {
  let chargesInfos = [];
  const resChargesInfos = priceInfo?.feeDetailInfo?.chargesInfos;
  if (resChargesInfos?.length > 0) {
    chargesInfos = resChargesInfos.map(feeItem => {
      const isCarRentalFee =
        feeItem.code === ServerMapping.FEE_CODES.CAR_RENTAL_FEE;
      const isAdditionalFee =
        feeItem.code === ServerMapping.FEE_CODES.CAR_ADDITIONAL_FEE;
      feeItem.currentDailyPrice = feeItem.currenctDailyPrice;
      const notPromotionCodes: string[] = [
        ServerMapping.FEE_CODES.RENTAL_FEE,
        ServerMapping.FEE_CODES.EASYLIFE_2024,
      ];

      const newFeeItem = produce(feeItem, draftFeeItem => {
        if (feeItem?.items?.length > 0) {
          draftFeeItem.items = feeItem.items.map(detailItem => ({
            ...detailItem,
            currentDailyPrice: detailItem.currenctDailyPrice,
            // 在车辆租金分组下,除租车费外认定其他都属于优惠
            isPromotion:
              isCarRentalFee && !notPromotionCodes.includes(detailItem.code), // todo-xt 待和服务端确认是否要调整判断逻辑
            size: Utils.isCtripIsd() || isAdditionalFee ? detailItem?.size : '',
          }));
        }
      });

      return newFeeItem;
    });
  }
  return {
    chargesInfos,
    chargesSummary: priceInfo?.feeDetailInfo?.chargesSummary,
    cashBackInfo: priceInfo?.feeDetailInfo?.cashBackInfo?.items[0],
    points: priceInfo?.feeDetailInfo?.points,
  };
});

export const getFooterBarData = createSelector<
  any,
  PriceInfoResponseType,
  FooterBarDataType | null
>([getPrice], priceInfo => {
  const payInfo = priceInfo?.payModeInfos?.find(v => v.isSelected);
  if (!payInfo) return null;
  const discountPrice = payInfo?.discountItems?.reduce(
    (m, v) => m + v.currentTotalPrice,
    0,
  );
  return {
    price: payInfo?.currenctPriceInfo?.actualAmount,
    originPrice: discountPrice
      ? parseInt(payInfo?.choiceNameOriginalAmount, 10)
      : 0,
    discountPrice,
    currencyCode: payInfo?.currenctPriceInfo.currencyCode,
  };
});

export const getHasVehicleConfig = createSelector<any, any, any, boolean>(
  [getBaseResData, getCurPriceInfo],
  (response, curPriceInfo) => {
    const allTags = curPriceInfo?.allTags;
    const vehicleInfo = response?.vehicleInfo;
    const userRealImageList = vehicleInfo?.userRealImageList || [];
    const storeRealImageList = vehicleInfo?.storeRealImageList || [];
    const hasImage = userRealImageList.length + storeRealImageList.length > 0;
    const hasVehicleTags =
      allTags?.filter(flex => flex.tagGroups === 1).length > 0;
    return hasImage || hasVehicleTags;
  },
);

export const getBookingStoreGuidInfos = createSelector<
  any,
  Array<IMergeGuidInfo>,
  Array<IMergeGuidInfo>,
  any,
  Array<IMergeGuidInfo>
>(
  [
    getQueryVehicleDetailInfoStoreGuidInfos,
    getQueryProductInfoStoreGuidInfos,
    getBookingFirstScreenParam,
  ],

  (
    queryVehicleDetailInfoStoreGuidInfos,
    queryProductInfoStoreGuidInfos,
    bookingFirstScreenParam,
  ) => {
    // 当信息确认弹层已有取还车信息时，填写页首屏优先使用
    if (bookingFirstScreenParam && queryVehicleDetailInfoStoreGuidInfos) {
      return queryVehicleDetailInfoStoreGuidInfos;
    }

    return queryProductInfoStoreGuidInfos;
  },
);

interface IBookingVendorInfoType {
  vendorName: string;
  isOptimize: boolean;
  nationalChainTagTitle: string;
  isEasyLife: boolean;
  allTagsInfo: IAllTagsInfoType;
  isSelfService?: boolean;
}

export const getNewBookingVendorInfo = createSelector<
  any,
  string,
  string,
  any,
  boolean,
  IBookingVendorInfoType
>(
  [
    getCurInsPackageId,
    getCurPackageId,
    getBookingFirstScreenParam,
    getBaseResData,
  ],

  (curInsPackageId, curPackageId, bookingFristScreenParam) => {
    // 当新详情页有透传首屏数据时，优先使用透传数据
    if (bookingFristScreenParam) {
      return {
        vendorName: bookingFristScreenParam?.vendorInfo?.vendorName,
        isOptimize: bookingFristScreenParam?.isSelect,
        nationalChainTagTitle: bookingFristScreenParam?.nationalChainTagTitle,
        isEasyLife: bookingFristScreenParam?.isEasyLife,
        allTagsInfo: bookingFristScreenParam?.allTags,
        isSelfService: bookingFristScreenParam?.isSelfService,
      };
    }
    let allTags = getPackageVendorProps(curInsPackageId, curPackageId)?.allTags;
    // 车型标签合并
    allTags = mergeVehicleYearLabel(allTags, 'tagSortNum');

    const nationalChainTag = getNationalChainTag(allTags);
    const isEasyLife = getCurPackageIsEasyLife();
    const isSelfService = !!allTags?.find(
      item => item?.labelCode === ILableCode.SelfService,
    );
    return {
      vendorName: getVendorInfo()?.vendorName,
      isOptimize: getBaseResData()?.isSelected,
      nationalChainTagTitle: nationalChainTag?.title,
      isEasyLife,
      allTagsInfo: getTagListInfo(allTags, isEasyLife),
      isSelfService,
    };
  },
);

export const getActivityDetail = createSelector([getPrice], price => {
  const activityDetail = price?.activityDetail;
  const marketingLabels = price?.marketingLabels;
  if (!marketingLabels?.length) return activityDetail;
  const newActivityDetail = produce(activityDetail, draftActivityDetail => {
    // 处理 promotions 数组, 匹配到市场标签后优先对对应市场标签的description;
    if (draftActivityDetail?.promotions?.length) {
      draftActivityDetail.promotions.forEach(promotion => {
        const activityLabel = marketingLabels.find(
          item => item.labelCode === promotion.labelCode,
        );
        if (activityLabel) {
          promotion.description = activityLabel.description;
        }
      });
    }
  });
  return newActivityDetail;
});

export const isLateDepositVerVendorBlackList = createSelector(
  [getPrice],
  price => {
    return (
      Number(price?.baseResponse?.extMap?.lateDepositVer) ===
      LateDepositVer.preSale
    );
    // 不支持售后免押的供应商
  },
);

export const getPolicyQueryParams = createSelector<
  any,
  VendorPriceListType,
  any,
  any,
  ReqInfoType
>(
  [getVendorPriceInfo, getPickUpTime, getDropOffTime],
  (priceInfo, pickUpTime, dropOffTime) => {
    return {
      storeCode: priceInfo?.reference.pStoreCode,
      rStoreCode: priceInfo?.reference.rStoreCode,
      vendorCode: priceInfo?.reference.vendorCode,
      isEasyLife: priceInfo?.reference.isEasyLife,
      pickUpDate: dayjs(pickUpTime).format('YYYY-MM-DD HH:mm:ss'),
      returnDate: dayjs(dropOffTime).format('YYYY-MM-DD HH:mm:ss'),
      ctripVehicleId: priceInfo?.ctripVehicleCode,
      vehicleId: priceInfo?.reference?.vehicleCode,
    };
  },
);

// 组装新版优惠和押金模块可享优惠提示文案
export const getNewCouponTip = createSelector([getPrice], priceInfo => {
  const preferentialTips = priceInfo?.preferentialTips;
  const membershipPerception = priceInfo?.membershipPerception;

  let couponTip = '';
  if (preferentialTips?.description) {
    const preTxt =
      Constants.MemberStyle[membershipPerception?.curLevelCode]?.booking
        ?.couponPreTxt || '已享';
    couponTip = preTxt + preferentialTips?.description;
  }

  return couponTip;
});

// 组装新版优惠和押金模块押金信息
export const getNewDepositData = createSelector([getPrice], priceInfo => {
  const depositPayInfos = priceInfo?.depositPayInfos;
  const isZero = priceInfo?.orderPriceInfo?.payAmount === 0;
  let depositTitle = '';
  let depositDescs = null;
  let opeButtonInfo = null;
  let fundStringObjs = null;
  let selectPassengerObj = null;
  let depositRuleObj = null;
  let hasRefundNote = null;
  let hasDepositRule = false;

  if (depositPayInfos?.length > 0) {
    const depositTypeInfo = depositPayInfos[0]?.depositTypeInfo;
    depositTitle = depositTypeInfo?.title?.stringObjs?.[0]?.content;

    opeButtonInfo = depositPayInfos.find(f => f.isClickable);

    if (depositTypeInfo?.desc?.length > 0) {
      const stringObjs = depositTypeInfo?.desc[0]?.stringObjs;
      hasRefundNote = stringObjs?.find(
        f => f.url === Enums.DepositDescUrlType.note,
      );
      if (hasRefundNote) {
        fundStringObjs = stringObjs;
      }

      selectPassengerObj = stringObjs?.find(
        f => f.url === Enums.DepositDescUrlType.SelectPassenger,
      );

      hasDepositRule = stringObjs?.find(
        f => f.url === Enums.DepositDescUrlType.depositRule,
      );

      if (hasDepositRule) {
        depositRuleObj = stringObjs;
      }
    }

    if (!hasRefundNote && !selectPassengerObj && !depositRuleObj) {
      depositDescs = depositTypeInfo?.desc;
    }
  }

  return {
    depositTitle,
    depositDescs,
    opeButtonInfo,
    fundStringObjs,
    selectPassengerObj,
    depositRuleObj,
    isZero,
  };
});

/**
 * 校验是否要将押金合并展示到优惠模块, 合并的场景有
 * - 程信分为T
 * - (程信分为N 或 depositFreeType为2）+ 芝麻状态为0、1、3、7、103
 * - 没选驾驶员
 */
export const validateIsMergeDeposit = createSelector(
  [getPrice, isLateDepositVerVendorBlackList],
  (priceInfo, lateDepositVer) => {
    if (!Utils.isCtripIsd()) {
      return false;
    }
    // 不支持免押后置的供应商也不合并
    if (lateDepositVer) {
      return false;
    }

    const { ZhimaResult, DepositFreeType } = ApiResCode;

    const trackInfo = priceInfo?.trackInfo;
    const depositPayInfos = priceInfo?.depositPayInfos;
    const isT = trackInfo?.riskFinal === Enums.HomeUserCreditStatus.T;
    const isN = trackInfo?.riskFinal === Enums.HomeUserCreditStatus.N;
    const justSupportZhima =
      trackInfo?.depositFreeType === DepositFreeType.zhima;
    let needSelectPassenger = false;
    if (depositPayInfos?.length > 0) {
      needSelectPassenger =
        depositPayInfos[0]?.depositTypeInfo?.desc?.[0]?.stringObjs?.findIndex(
          f => f.url === Enums.DepositDescUrlType.SelectPassenger,
        ) > -1;
    }
    if (isT) {
      return true;
    }

    if (
      ((trackInfo?.depositFreeType === DepositFreeType.riskeAndZhima && isN) ||
        justSupportZhima) &&
      [
        ZhimaResult.unAuthorized,
        ZhimaResult.authorized,
        ZhimaResult.failAuthorized,
        ZhimaResult.partAuthorized,
        ZhimaResult.insufficientQuota,
      ].includes(Number(trackInfo?.zhimaResult))
    ) {
      return true;
    }
    if (needSelectPassenger) {
      return true;
    }

    return false;
  },
);

export const getDepositTip = createSelector(
  [getPrice, getDepositPayType],
  (priceInfo, depositPayType) => {
    const trackInfo = priceInfo?.trackInfo;
    const isT = trackInfo?.riskFinal === Enums.HomeUserCreditStatus.T;
    const isBothFree = depositPayType === Enums.DepositPayType.BothFree;
    // 当前选中的是诚信分
    const isChengXinFen = isT && isBothFree;
    const isZero = priceInfo?.orderPriceInfo?.payAmount === 0;
    if (!isChengXinFen) {
      return '';
    }
    const addOnServices = priceInfo?.addOn?.addOnServices;
    const rentalDeposit = addOnServices?.find(
      v => v.uniqueCode === ServerMapping.FEE_CODES.RENTAL_DEPOSIT,
    );
    const breakDeposit = addOnServices?.find(
      v => v.uniqueCode === ServerMapping.FEE_CODES.BREAK_DEPOSIT,
    );
    if (!rentalDeposit || !breakDeposit || isZero) {
      return '';
    }
    return `支付时授权，可享免租车押金¥${rentalDeposit.cnyTotalPrice}和违章押金¥${breakDeposit.cnyTotalPrice}`;
  },
);

export const getAuthInfo = createSelector([getSesameInfo], sesameInfo => {
  const { authInfos = [], authStatus } = sesameInfo || {};
  const authInfo = authInfos?.[0];
  const { contents = [], type, note, button } = authInfo || {};
  const content: string[] = [];
  contents.forEach(item => {
    let inlineDesc = '';
    item?.stringObjs?.forEach(stringObjItem => {
      inlineDesc += stringObjItem?.content;
    });
    content.push(inlineDesc);
  });

  return {
    authStatus,
    type,
    content,
    note,
    button,
  };
});

export const getDepositDescriptionSectionData = createSelector(
  [
    getCurInsPackageId,
    getBaseResData,
    getDepositInfo,
    getDepositRateDescriptionContent,
  ],

  (
    curInsPackageId,
    baseResData,
    depositInfo,
    depositRateDescriptionContent,
  ) => {
    const { pickUpMaterials = [] } = getCurProductInfo(curInsPackageId) || {};
    const creditCardDepositInfo =
      pickUpMaterials.find(
        item => item.type === MaterialsType.CreditCardDepositInfo,
      ) || {};
    const { urlList = [] } =
      creditCardDepositInfo?.subObject?.find(
        item =>
          item.type ===
          (Utils.isCtripOsd()
            ? PickUpMaterials.CreditInfo
            : MaterialsType.CreditCard),
      ) || {};
    const { title, items = [], notices = [] } = depositInfo || {};
    const fixItems: IDepositDescriptionType[] = [];
    items.forEach(item => {
      fixItems.push({
        title:
          item.title?.length > 2
            ? `${item.title.slice(0, 2)}\n${item.title.slice(2)}`
            : item.title,
        content: item?.contents?.[0]?.stringObjs?.[0]?.content,
        description: item?.description,
        isShowFree: item?.showFree,
        positiveDesc: item?.positiveDesc,
        isShowQuestion:
          !!item?.contents?.[0]?.stringObjs?.[0]?.content &&
          !!depositRateDescriptionContent,
        creditCardImgList: item?.showCreditCard && urlList,
      });
    });

    return {
      title,
      items: fixItems,
      notices,
    };
  },
);

export const getDepositMethodData = createSelector(
  [getDepositPayInfos, getSesameInfo],
  (depositPayInfos, sesameInfo) => {
    const depositPays: IDepositMethodItem[] = depositPayInfos?.map(item => ({
      depositPayType: item.depositPayType,
      isEnable: item.isEnable,
      isCheck: item.isCheck,
      title: item?.depositTypeInfo?.title?.stringObjs?.[0]?.content,
      desc: item?.depositTypeInfo?.desc?.[0]?.stringObjs?.[0]?.content,
    }));
    return {
      depositPays,
      isShowAuthInfo: sesameInfo?.authInfos?.length > 0,
      authType: sesameInfo?.authInfos?.[0]?.button?.type,
    };
  },
);

export const getIsSelfService = createSelector<any, any, boolean>(
  [getCurPriceInfo],
  curPriceInfo =>
    !!curPriceInfo?.allTags?.find(
      item => item?.labelCode === ILableCode.SelfService,
    ),
);
export const getLocalContactSelectedType = createSelector(
  [getDriverInfo],
  driverInfo => {
    if (driverInfo?.length) {
      const localContactData = driverInfo.find(
        item => item.type === IInputType.localContacts,
      );
      return localContactData?.contactType;
    }
    return 0;
  },
);

export const getLocalContactsData = state => state.Booking.localContactsData;
