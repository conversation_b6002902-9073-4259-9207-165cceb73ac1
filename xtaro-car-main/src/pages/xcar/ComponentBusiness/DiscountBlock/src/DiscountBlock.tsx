import Image from '@c2x/components/Image';
import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard'; /* eslint-disable */
import c2xStyles from './discountBlockC2xStyles.module.scss';
import React, { useCallback, useState, CSSProperties } from 'react';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Layer from '@ctrip/rn_com_car/dist/src/Components/Basic/Layer';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import BbkChannel, { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, color, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';
import * as ImageUrl from '../../../Constants/ImageUrl';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { texts } from './Texts';
import { UITestID } from '../../../Constants/Index';

const { selector, getPixel } = BbkUtils;

export interface IDiscountItem {
  title: string;
  label?: string;
  desc?: string;
  currency: string;
  price: number;
}

export interface IDiscount {
  isLogin: boolean;
  isActivity?: boolean;
  disableText?: string;
  discountItems?: IDiscountItem[];
  showIcon?: boolean;
  enablePress?: boolean;
  onPressCoupon?: () => void;
  onPressActivity?: () => void;
  theme?: any;
  style?: CSSProperties;
  titleStyle?: CSSProperties;
  discountItemStyle?: CSSProperties;
  priceExtraStyle?: CSSProperties;
  marginStyle?: CSSProperties;
  couponTipStyle?: CSSProperties;
}

const DiscountBlock: React.FC<IDiscount> = ({
  isLogin,
  isActivity = false,
  discountItems,
  showIcon = true,
  enablePress = true,
  onPressCoupon,
  onPressActivity,
  disableText,
  theme,
  style,
  titleStyle,
  discountItemStyle,
  priceExtraStyle = {},
  marginStyle,
  couponTipStyle = {},
}) => {
  const themes: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  const {
    bbkDiscountBlockAvailableColor = color.blueBase,
    bbkDiscountBlockUnusedColor = color.fontPrimary,
    bbkDiscountBlockCurrencyColor = color.orangePrice,
    bbkDiscountBlockLabelColor = color.orangePrice,
    bbkDiscountBlockLabelBackgroundColor = color.orangeBgLight,
    bbkDiscountBlockEditIconColor = color.fontSecondary,
    bbkDiscountBlockQuestionIconColor = color.grayBase,
  } = themes;
  const [visible, setVisible] = useState(false);
  const [currentDesc, setCurrentDesc] = useState('');
  const handleIconPress = useCallback(
    desc => {
      if (isActivity) {
        Keyboard.dismiss();
        setVisible(true);
        setCurrentDesc(desc);
        onPressActivity && onPressActivity();
        return;
      }
      onPressCoupon();
    },
    [onPressCoupon, isActivity],
  );
  const showPrice = !!discountItems?.length && isLogin;
  const showLogin = !isLogin;
  const priceStyle = {
    ...font.title2Style,
    color: bbkDiscountBlockCurrencyColor,
    ...priceExtraStyle,
  };
  const title = isActivity ? texts.couponActivity : texts.couponPromoCode;
  if (!showLogin && !disableText && !discountItems?.length) return null;
  if (!discountItems?.length) return null;
  return (
    <React.Fragment>
      <View
        className={c2xStyles.container}
        style={xMergeStyles([!!discountItems.length && styles.wrapper, style])}
      >
        {selector(
          !showLogin,
          <View
            style={xMergeStyles([{ marginBottom: getPixel(32) }, marginStyle])}
          >
            <Text
              style={xMergeStyles([{ ...font.title1MediumStyle }, titleStyle])}
            >
              {title}
            </Text>
          </View>,
          null,
        )}
        {discountItems.map((discountItem, index) => {
          const Wrapper =
            (!isActivity || (discountItem && discountItem.desc)) && enablePress
              ? Touchable
              : View;
          return (
            <Wrapper
              key={`${discountItem.title}-${index}`}
              style={styles.flexRow}
              onPress={() => {
                handleIconPress(discountItem.desc);
              }}
            >
              <View className={c2xStyles.flex1}>
                {!showLogin && (
                  <View className={c2xStyles.couponTitle}>
                    {showIcon && (
                      <Image
                        src={`${ImageUrl.BBK_IMAGE_PATH}coupon_book2.png`}
                        mode="aspectFit"
                        className={c2xStyles.couponImg}
                      />
                    )}
                    <View className={c2xStyles.flex1}>
                      {!!(discountItem && discountItem.title) && (
                        <Text
                          className={c2xStyles.titleText}
                          style={discountItemStyle}
                        >
                          {discountItem.title}
                        </Text>
                      )}
                      {!!disableText && (
                        <Text
                          className={c2xStyles.couponTip}
                          style={xMergeStyles([
                            { color: bbkDiscountBlockUnusedColor },
                            couponTipStyle,
                          ])}
                        >
                          {disableText}
                        </Text>
                      )}
                      {!!(discountItem && discountItem.label) && (
                        <View
                          className={c2xStyles.flexRow}
                          style={{ marginTop: getPixel(4) }}
                        >
                          <View
                            className={c2xStyles.labelWrapper}
                            style={{
                              backgroundColor:
                                bbkDiscountBlockLabelBackgroundColor,
                            }}
                          >
                            <Text
                              className={c2xStyles.labelText}
                              style={{ color: bbkDiscountBlockLabelColor }}
                            >
                              {discountItem.label}
                            </Text>
                          </View>
                        </View>
                      )}
                    </View>
                  </View>
                )}
                {showLogin && (
                  <View>
                    <Text style={font.title1MediumStyle} fontWeight="medium">
                      {title}
                    </Text>
                  </View>
                )}
              </View>
              <View className={c2xStyles.couponPrice}>
                {showPrice && (
                  <View
                    style={xMergeStyles([
                      priceStyle,
                      {
                        flexDirection: 'row',
                        alignItems: 'center',
                      },
                    ])}
                  >
                    <Text style={priceStyle}>-&nbsp;</Text>
                    <BbkCurrencyFormatter
                      currency={discountItem.currency}
                      price={discountItem.price}
                      currencyStyle={priceStyle}
                      priceStyle={priceStyle}
                    />
                  </View>
                )}
                {showLogin && (
                  <Text
                    className={c2xStyles.couponTip}
                    style={{ color: bbkDiscountBlockAvailableColor }}
                  >
                    {texts.listSignIn}
                  </Text>
                )}
                {enablePress &&
                  selector(
                    isActivity,
                    selector(
                      discountItem && discountItem.desc,
                      <Text
                        type="icon"
                        className={c2xStyles.icon}
                        style={{ color: bbkDiscountBlockEditIconColor }}
                      >
                        {icon.arrowRight}
                      </Text>,
                    ),
                    <Text
                      type="icon"
                      className={c2xStyles.iconEdit}
                      style={{ color: bbkDiscountBlockEditIconColor }}
                    >
                      {icon.arrowRight}
                    </Text>,
                  )}
              </View>
            </Wrapper>
          );
        })}
      </View>
      {!!currentDesc && (
        <Layer
          style={layout.flexRow}
          modalVisible={visible}
          title={texts.couponActivity}
          onRequestClose={() => setVisible(false)}
        >
          <Text>{currentDesc}</Text>
        </Layer>
      )}
    </React.Fragment>
  );
};

const styles = StyleSheet.create({
  wrapper: { paddingBottom: getPixel(32) },
  flexRow: { flexDirection: 'row', alignItems: 'center' },
});

export default withTheme(DiscountBlock);
