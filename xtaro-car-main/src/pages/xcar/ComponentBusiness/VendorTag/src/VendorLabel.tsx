import { set as lodashSet } from 'lodash-es';
/* eslint-disable prefer-destructuring */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { CSSProperties } from 'react';
import { xMergeStyles } from '@ctrip/xtaro';
import memoize from 'memoize-one';
import {
  tokenType,
  icon,
  color,
  setOpacity,
  label,
  space,
  border,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import Utils from '../../../Util/Utils';
import styles from './VendorStyle';
import { Enums } from '../../Common/index';
import { AppContext, CarABTesting, GetAB } from '../../../Util/Index';
import Channel from '../../../Util/Channel';
import { ImageUrl } from '../../../Constants/Index';
import EasyLifeLabel from './EasyLifeLabel';
import CarCenterLabel from './CarCenterLabel';
import { texts } from './Texts';

const { ColorCodeType } = Enums;
const { getPixel, isAndroid, getLineHeight } = BbkUtils;
const labelHeight = 34;
export interface ILabelProps {
  tag?: VendorTagType;
  labelStyle?: CSSProperties | Array<CSSProperties>;
  textStyle?: CSSProperties;
  prefixStyle?: CSSProperties;
  prefixWrapStyle?: CSSProperties | Array<CSSProperties>;
  serrationStyle?: CSSProperties | Array<CSSProperties>;
  theme?: any;
  isLast?: boolean;
  testID?: string;
}

const zhimaHeight = label.baseLabelLHeight + border.borderSizeXsm * 2;

// 大类(每行)4种
// 第一行(type=1)：取消、确认
// 第二行(type=2)：正向
// 第三行(type=3)：营销
// 第四行(type=4)：负向
export const lineType = {
  1: {
    lineKey: 'policy',
    type: '1',
  },
  2: {
    lineKey: 'positive',
    type: '2',
  },
  3: {
    lineKey: 'promotion',
    type: '3',
  },
  4: {
    lineKey: 'negative',
    type: '4',
  },
};

const newBlueLabelStyleNew = {
  codeKey: 'blueLabel',
  colorType: tokenType.ColorType.Blue,
  hasBorder: true,
  labelStyle: {
    borderColor: setOpacity(color.C_4673BE, 0.25),
    borderWidth: getPixel(1),
  },
  textStyle: {
    color: color.C_4673B2,
    top: getPixel(isAndroid ? -2.5 : -1),
    ...font.F_22_12_regular,
  },
};
const marketCouponLabelStyleNew = {
  codeKey: 'marketCouponLabel',
  hasBorder: true,
  isNewDash: true,
  textStyle: {
    color: color.orangePrice,
    ...font.F_22_10_regular,
    top: getPixel(isAndroid ? -2.5 : -1),
  },
  postfixStyle: {
    color: color.orangePrice,
    ...font.F_22_10_regular,
    top: getPixel(isAndroid ? 0 : -1),
  },
  labelStyle: {
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    backgroundColor: color.transparent,
    borderColor: color.transparent,
    borderWidth: getPixel(1),
    marginRight: 0,
  },
  postfixWrapStyle: {
    backgroundColor: color.transparent,
    marginTop: 0,
  },
  borColor: color.labelMarketBorder,
  icon: {
    iconContent: null,
  },
};
const correctLabelStyleNew = {
  codeKey: 'correctLabel',
  colorType: tokenType.ColorType.Green,
  labelSize: 'XL',
  labelStyle: {
    paddingLeft: 0,
    paddingRight: getPixel(8),
  },
  textStyle: {
    color: color.C_01AE73,
  },
};
const redLabelStyleNew = {
  codeKey: 'redLabel',
  colorType: tokenType.ColorType.Red,
  hasBorder: true,
  labelStyle: {
    paddingLeft: 0,
    borderColor: setOpacity(color.C_aaa, 0.25),
    borderWidth: getPixel(1),
  },
  textStyle: {
    color: color.C_777777,
    top: getPixel(isAndroid ? -2.5 : -1),
    ...font.F_22_12_regular,
  },
};
const grayLabelNew = {
  codeKey: 'grayLabel',
  colorType: tokenType.ColorType.Gray,
  hasBorder: true,
  textStyle: {
    ...font.F_22_10_regular,
    top: getPixel(isAndroid ? -2 : 0),
  },
};
// 小类(样式)7种
// code=1：黑色
// code=2：蓝色
// code=3：橘色（营销）
// code=4: 红色
// code=5：绿色
// code=6：灰色
// code=7：芝麻
// code=8：取消，确认（勾、绿色）
const getCodeType = () => ({
  1: {
    codeKey: 'blackLabel',
    colorType: tokenType.ColorType.Black,
    labelSize: 'XL',
    labelStyle: {
      paddingLeft: -space.spaceXS,
      paddingRight: -space.spaceXS,
    },
  },
  2: GetAB.isISDInterestPoints()
    ? newBlueLabelStyleNew
    : {
        codeKey: 'blueLabel',
        colorType: tokenType.ColorType.Blue,
        hasBorder: true,
      },
  3: {
    codeKey: 'discountLabel',
    isSerration: true,
    hasBorder: true,
    textStyle: {
      color: color.orangePrice,
      ...font.labelSLightStyle,
      lineHeight: getLineHeight(28),
    },
    postfixStyle: {
      color: color.orangePrice,
      ...font.labelSLightStyle,
      lineHeight: getLineHeight(28),
    },
    labelStyle: {
      minHeight: getPixel(32),
      paddingLeft: getPixel(8),
      paddingRight: getPixel(8),
      backgroundColor: color.labelPostfixBg,
      borderColor: setOpacity(color.orangePrice, 0.2),
      borderWidth: getPixel(1),
    },
    icon: {
      iconContent: null,
    },
  },
  4: GetAB.isISDInterestPoints()
    ? redLabelStyleNew
    : {
        codeKey: 'redLabel',
        colorType: tokenType.ColorType.Red,
        hasBorder: true,
        theme: {},
        labelStyle: {
          paddingLeft: 0,
        },
        icon: Utils.isCtripIsd()
          ? {}
          : {
              iconStyle: {
                marginRight: 0,
              },
              iconContent: icon.circleWithSigh,
            },
      },
  5: {
    codeKey: 'greenLabel',
    colorType: tokenType.ColorType.Green,
    hasBorder: true,
  },
  6: GetAB.isISDInterestPoints()
    ? grayLabelNew
    : {
        codeKey: 'grayLabel',
        colorType: tokenType.ColorType.Gray,
        hasBorder: true,
      },
  7: {
    codeKey: 'zhimaLabel',
    hasBorder: true,
    textStyle: {
      color: color.sesamePrimary,
    },
    labelStyle: {
      position: 'relative',
      paddingLeft: label.baseLabelLHeight + space.spaceS,
      color: color.sesamePrimary,
      borderColor: setOpacity(color.sesamePrimary, 0.3),
    },
    icon: {
      iconWrapStyle: {
        position: 'absolute',
        top: -border.borderSizeXsm,
        bottom: -border.borderSizeXsm,
        left: 0,
        borderRadius: getPixel(2),
        borderTopRightRadius: getPixel(0),
        borderBottomRightRadius: getPixel(0),
        marginRight: 0,
        overflow: 'hidden',
      },
      iconStyle: {
        width: zhimaHeight,
        height: zhimaHeight,
        lineHeight: getLineHeight(zhimaHeight),
        color: color.white,
        backgroundColor: color.sesamePrimary,
        borderTopRightRadius: 0,
        borderBottomRightRadius: 0,
      },
      iconContent: icon.zhima,
    },
  },
  10: {
    codeKey: 'creditRentLabel',
    hasBorder: true,
    prefixWrapStyle: Utils.isCtripIsd()
      ? {
          backgroundColor: setOpacity(color.blueBase, 0.1),
        }
      : {},
    textStyle: {
      color: color.blueBase,
    },
    prefixStyle: Utils.isCtripIsd()
      ? {
          color: color.blueBase,
        }
      : {},
    labelStyle: {
      paddingLeft: getPixel(8),
      paddingRight: getPixel(8),
      backgroundColor: Utils.isCtripIsd() ? color.transparent : color.tableBg,
      borderColor: color.blueBorder,
      borderRadius: getPixel(4),
    },
    icon: {
      iconContent: null,
    },
  },
  // 新版营销带锯齿标签  是否要走AB样式，取决于新标签类型是否存在于已有的code
  15: GetAB.isISDInterestPoints()
    ? marketCouponLabelStyleNew
    : {
        codeKey: 'marketCouponLabel',
        isSerration: true,
        hasBorder: true,
        textStyle: {
          color: color.orangePrice,
          ...font.labelSLightStyle,
          lineHeight: getLineHeight(28),
        },
        postfixStyle: {
          color: color.orangePrice,
          ...font.labelSLightStyle,
          lineHeight: getLineHeight(28),
        },
        labelStyle: {
          paddingLeft: getPixel(8),
          paddingRight: getPixel(8),
          backgroundColor: color.transparent,
          borderColor: color.labelMarketBorder,
          borderWidth: getPixel(1),
        },
        borColor: color.labelMarketBorder,
        icon: {
          iconContent: null,
        },
      },
  8: GetAB.isISDInterestPoints()
    ? correctLabelStyleNew
    : {
        codeKey: 'correctLabel',
        colorType: tokenType.ColorType.Green,
        labelSize: 'XL',
        labelStyle: Utils.isCtripIsd()
          ? { paddingLeft: 0, paddingRight: getPixel(8) }
          : {},
        icon: Utils.isCtripIsd()
          ? {}
          : {
              iconContent: icon.tickStrong,
              iconWrapStyle: {
                marginLeft: -BbkUtils.getPixel(4),
              },
              iconStyle: {
                marginLeft: -BbkUtils.getPixel(2),
              },
            },
      },
  11: {
    codeKey: 'orangeLabel',
    colorType: tokenType.ColorType.Orange,
    hasBorder: true,
    theme: {},
  },
  12: {
    codeKey: 'orangeLabel',
    colorType: tokenType.ColorType.Orange,
    hasBorder: true,
    theme: {},
  },
  13: {
    prefixImage: `${ImageUrl.rncarappBasicUrl}11.11/1111_label.png`,

    codeKey: 'elevenHolidayLabel',
    hasBorder: false,
    prefixWrapStyle: {
      borderColor: color.elevenFestivalLabelBorderColor,
      borderWidth: border.borderSizeXsm,
      borderLeftWidth: 0,
      overflow: 'hidden',
      borderBottomRightRadius: 1,
      borderTopRightRadius: 1,
      borderBottomLeftRadius: 0,
      borderTopLeftRadius: 0,
      paddingLeft: getPixel(8),
      paddingRight: getPixel(8),
      backgroundColor: color.white,
      marginLeft: 0,
      marginRight: 0,
    },
    prefixStyle: {
      color: color.elevenFestivalLabelColor,
      flexWrap: 'wrap',
    },
    labelStyle: {
      paddingLeft: 0,
      paddingRight: getPixel(8),
      height: getPixel(34),
    },
    prefixImageStyle: {
      width: getPixel(129),
      height: getPixel(34),
    },
  },
  14: {
    prefixImage: `${ImageUrl.CTRIP_EROS_URL}vendor_easy_life_logo.png`,

    codeKey: 'easyLifeSet',
    hasBorder: true,
    prefixWrapStyle: {
      borderColor: color.blueBase,
      borderWidth: border.borderSizeXsm,
      overflow: 'hidden',
      backgroundColor: color.white,
    },
    labelStyle: {
      paddingLeft: 0,
      paddingRight: getPixel(8),
      height: getPixel(28),
    },
    prefixImageStyle: {
      width: getPixel(71),
      height: getPixel(32),
      marginRight: getPixel(4),
    },
    labelSize: 'SLightFlat',
  },
});

const VendorLabel = ({
  tag = {},
  theme,
  labelStyle,
  textStyle,
  prefixStyle,
  prefixWrapStyle,
  serrationStyle,
  isLast,
  testID,
}: ILabelProps) => {
  const {
    colorCode,
    title = '',
    amountTitle = '',
    prefix,
    subTitle = '',
  } = tag;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  const { soldOutTextColor, soldOutLabelBgColor } = theme;
  const codeType = getCodeType();
  const marketTagMaxByteLength = isISDInterestPoints ? 15 : 14;
  console.log('codeType=====>', codeType);
  console.log('tag=====>', tag);
  if (isISDInterestPoints && colorCode === '14') {
    return <EasyLifeLabel title={title} soldOutTextColor={soldOutTextColor} />;
  }
  if (isISDInterestPoints && tag?.prefix === texts.carRentalCenterLabel) {
    return <CarCenterLabel title={title} soldOutTextColor={soldOutTextColor} />;
  }
  if (!codeType[colorCode]) {
    return null;
  }
  const codeProps = BbkUtils.cloneDeep(codeType[colorCode]);

  const splits = title.split('·');
  if (colorCode === ColorCodeType.CreditRent && splits.length > 1) {
    if (!Utils.isCtripIsd()) {
      codeProps.prefix = splits[0];
    }
    codeProps.text = splits[1];
  }

  if (colorCode === ColorCodeType.MarketCoupon) {
    codeProps.text = title;
    codeProps.postfix = amountTitle;
    // 营销标签字节数控制
    if (
      Utils.getByteLength(title) > marketTagMaxByteLength &&
      !!title &&
      !!amountTitle
    ) {
      codeProps.text = `${Utils.getByteLengthStr(
        title,
        marketTagMaxByteLength,
      )}...`;
    }
  }

  // soldout style
  if (soldOutTextColor) {
    const { icon: iconProps = {} } = codeProps;
    const { iconStyle } = iconProps;

    lodashSet(codeProps, 'isSoldOut', true);

    if (colorCode === ColorCodeType.CreditRent) {
      lodashSet(codeProps, 'labelStyle.backgroundColor', color.transparent);
    }

    lodashSet(codeProps, 'textStyle.color', soldOutTextColor);
    lodashSet(
      codeProps,
      'labelStyle.borderColor',
      soldOutLabelBgColor || soldOutTextColor,
    );

    if (iconStyle) {
      lodashSet(iconStyle, 'color', soldOutTextColor);
      lodashSet(iconStyle, 'backgroundColor', color.transparent);
    } else {
      lodashSet(codeProps, 'icon.iconStyle', { color: soldOutTextColor });
    }

    if (prefix || codeProps.prefix) {
      lodashSet(codeProps, 'prefixWrapStyle.backgroundColor', soldOutTextColor);
      if (Utils.isCtripIsd()) {
        lodashSet(
          codeProps,
          'prefixWrapStyle.backgroundColor',
          setOpacity(soldOutTextColor, 0.1),
        );
        lodashSet(codeProps, 'prefixStyle.color', soldOutTextColor);
      }
    }

    if (codeProps?.postfix) {
      lodashSet(
        codeProps,
        'postfixWrapStyle.backgroundColor',
        color.labelSoldBg,
      );
      lodashSet(codeProps, 'postfixStyle.color', soldOutTextColor);
      lodashSet(codeProps, 'borColor', soldOutLabelBgColor || soldOutTextColor);
    }

    if (colorCode === '7') {
      lodashSet(iconStyle, 'marginRight', 0);
      lodashSet(iconStyle, 'color', color.white);
      lodashSet(iconStyle, 'backgroundColor', soldOutTextColor);
    }

    if (colorCode === '14') {
      lodashSet(
        codeProps,
        'prefixImage',
        isISDInterestPoints
          ? `${ImageUrl.DIMG04_PATH}1tg5t12000d0mxdroE0D1.png`
          : `${ImageUrl.CTRIP_EROS_URL}vendor_easy_life_logo_gray.png`,
      );
    }
  }

  if (Utils.isCtripIsd()) {
    lodashSet(
      codeProps,
      'labelStyle.minHeight',
      getPixel(Utils.isCtripIsd() ? 32 : 30),
    );
    lodashSet(
      codeProps,
      'labelStyle.height',
      getPixel(Utils.isCtripIsd() ? 32 : 30),
    );
    if (codeProps.hasBorder) {
      if (!codeProps?.postfix) {
        lodashSet(codeProps, 'labelSize', 'SLightFlat');
        lodashSet(
          codeProps,
          'labelStyle.marginRight',
          getPixel(isLast ? 0 : 8),
        );
      }
      if (!codeProps.prefixImage && !codeProps?.postfix) {
        lodashSet(codeProps, 'labelStyle.paddingLeft', getPixel(8));
        lodashSet(codeProps, 'labelStyle.paddingRight', getPixel(8));
      }
      if (Utils.isCtripIsd()) {
        lodashSet(
          codeProps,
          'textStyle.lineHeight',
          getLineHeight(isAndroid ? 25 : 30),
        );
      } else {
        lodashSet(
          codeProps,
          'textStyle.lineHeight',
          getLineHeight(isAndroid ? 26 : 28),
        );
      }
    } else {
      lodashSet(codeProps, 'labelSize', 'XL');
      lodashSet(codeProps, 'labelStyle.paddingHorizontal', 0);
      lodashSet(codeProps, 'labelStyle.paddingLeft', 0);
      lodashSet(codeProps, 'labelStyle.paddingRight', 0);
      lodashSet(codeProps, 'labelStyle.marginBottom', 0);
      if (Utils.isCtripIsd()) {
        lodashSet(
          codeProps,
          'textStyle.lineHeight',
          getLineHeight(isAndroid ? 28 : 32),
        );
      } else {
        lodashSet(
          codeProps,
          'textStyle.lineHeight',
          getLineHeight(isAndroid ? 26 : 30),
        );
      }
    }
    // gray label
    if (colorCode === '6') {
      lodashSet(codeProps, 'textStyle.color', color.fontSecondary);
      lodashSet(
        codeProps,
        'labelStyle.borderColor',
        setOpacity(color.fontSecondary, 0.2),
      );
    }

    // 营销标签
    if (colorCode === '15' && !isISDInterestPoints) {
      lodashSet(codeProps, 'labelStyle.minHeight', getPixel(29));
      lodashSet(codeProps, 'labelStyle.height', getPixel(29));
      lodashSet(codeProps, 'textStyle.lineHeight', getLineHeight(28));
      lodashSet(codeProps, 'postfixWrapStyle.height', getPixel(28));
      if (isAndroid) {
        lodashSet(codeProps, 'textStyle.lineHeight', getLineHeight(26));
        lodashSet(codeProps, 'prefixWrapStyle.height', getPixel(26));
        lodashSet(codeProps, 'prefixStyle.lineHeight', getLineHeight(26));
        lodashSet(codeProps, 'postfixStyle.lineHeight', getLineHeight(26));
      }
    }
    if (colorCode === '14' && !isISDInterestPoints) {
      lodashSet(codeProps, 'prefixImageStyle', {
        width: getPixel(70),
        height: getPixel(Utils.isCtripIsd() ? 32 : 29.34),
        marginRight: getPixel(4),
      });
      lodashSet(codeProps, 'isEasyLife', true);
    }
  }

  if (
    Utils.isCtripIsd() &&
    AppContext.PageInstance.getPageId() === Channel.getPageId().Book.ID
  ) {
    // black label
    if (colorCode === '1') {
      lodashSet(codeProps, 'textStyle.color', color.recommendProposeBg);
    }
    // blue Label
    if (colorCode === '2' && !isISDInterestPoints) {
      lodashSet(codeProps, 'textStyle.color', color.bookingOptimizationBlue);
      lodashSet(
        codeProps,
        'labelStyle.borderColor',
        setOpacity(color.bookingOptimizationBlue, 0.2),
      );
      lodashSet(codeProps, 'labelStyle.backgroundColor', color.white);
    }
  }

  let handledLabelStyle = {};

  if (Array.isArray(labelStyle)) {
    labelStyle.forEach(item => {
      handledLabelStyle = { ...handledLabelStyle, ...item };
    });
  } else {
    handledLabelStyle = { ...labelStyle };
  }

  codeProps.labelStyle = {
    ...codeProps.labelStyle,
    borderRadius: getPixel(
      Utils.isCtripIsd() && colorCode !== ColorCodeType.MarketCoupon ? 4 : 2,
    ),
    transform: 'translateZ(0)',
    ...handledLabelStyle,
  };
  let labelText = title;
  switch (colorCode) {
    case ColorCodeType.ElevenHoliday:
      labelText = title.replace(subTitle, '');
      break;
    case ColorCodeType.RentHoliday:
      labelText = subTitle;
      break;
  }
  const labelProps = {
    noBg: true,
    text: labelText,
    prefix,
    labelSize: 'L',
    serrationStyle,
    ...codeProps,
  };
  if (colorCode === ColorCodeType.ElevenHoliday) {
    labelProps.text = '';
    labelProps.prefix = labelText;
  }
  // 如果是利益点优化，更改标签高度
  if (isISDInterestPoints) {
    lodashSet(codeProps, 'labelStyle.minHeight', getPixel(labelHeight));
    lodashSet(codeProps, 'labelStyle.height', getPixel(labelHeight));
    lodashSet(codeProps, 'textStyle.lineHeight', getLineHeight(labelHeight));
  }
  return (
    <BbkLabel
      {...labelProps}
      labelStyle={xMergeStyles([
        styles.vendorLabel,
        labelProps.labelStyle,
        labelStyle,
      ])}
      textStyle={xMergeStyles([labelProps.textStyle, textStyle])}
      prefixStyle={xMergeStyles([
        labelProps.prefixStyle,
        prefixStyle,
        soldOutTextColor && { color: soldOutTextColor },
      ])}
      prefixWrapStyle={xMergeStyles([
        labelProps.prefixWrapStyle,
        prefixWrapStyle,
      ])}
      testID={testID}
    />
  );
};

export default withTheme(VendorLabel);
