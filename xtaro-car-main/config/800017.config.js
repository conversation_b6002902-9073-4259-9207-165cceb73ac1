const path = require('path');
const routeConfig = require('./miniRouteConfig');
module.exports = {
  extraPackageJson: {
    dependencies: {
      '@ctrip/xtaro-style': '2.0.12',
      'lodash-es':
        'git+ssh://****************************:car/lodash-es-patch.git#main', 
      'await-to-js': '^2.1.1',
      'mini-html-parser2': '^0.3.0',
    },
  },
  alias: {
    '@ctrip/rn_com_car/dist': path.resolve(
      __dirname,
      '..',
      'src/pages/xcar/Common',
    ),
    '@c2x': path.resolve(__dirname, '..', 'src/pages/xcar/c2x'),
    '@platformStyle': path.resolve(__dirname, '..', 'src/pages/qunar/style'),
    '@platformComponents': path.resolve(
      __dirname,
      '..',
      'src/pages/qunar/components',
    ),
    '@/common': process.env.XTARO_APPID === '800017' ? path.resolve(__dirname, '..', 'src/pages/qunar/common') : path.resolve(__dirname, '..', 'src/pages/qunar/commonWX'),
    '@miniChannel': path.resolve(
      __dirname,
      '..',
      'src/pages/xcar/Diff/utils/channel',
    ),
    '@miniComponents': path.resolve(
      __dirname,
      '..',
      'src/pages/xcar/Diff/component',
    ),
    '@tokenColorScss':
      process.env.XTARO_APPID === '800017' ||
      process.env.XTARO_APPID === '800018'
        ? path.resolve(
            __dirname,
            '..',
            'src/pages/xcar/Common/src/Tokens/tokens/color.qunar.scss',
          )
        : path.resolve(
            __dirname,
            '..',
            'src/pages/xcar/Common/src/Tokens/tokens/color.scss',
          ),
  },
  routes: [
    ...routeConfig.routes,
    {
      path: 'pages/qunar/subPages/platform/userCenter/index',
      subRoot: 'pages/qunar/subPages/platform/userCenter',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/login/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/login',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/orderList/orderList/index',
      subRoot: 'pages/qunar/subPages/orderList/orderList',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/webView/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/webView',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/platform/myPage/index',
      subRoot: 'pages/qunar/subPages/platform/myPage',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/coupon/list/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/coupon/list',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/coupon/list/index',
      subRoot: 'pages/qunar/subPages/coupon/list',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/pay/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/pay',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/cqpay/holdpay/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/cqpay/holdpay',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
    {
      path: 'pages/qunar/subPages/alonePlatform/cqpay/protocol/index',
      subRoot: 'pages/qunar/subPages/alonePlatform/cqpay/protocol',
      crnRouterName: '',
      h5RouterName: '',
      platform: ['mini'],
    },
  ],
  pipeline: {
    alipay: 'qunar-alipay-release',
  },
  gitConfig: {
    baseConfig: {
      git: '****************************:car/xtaro-car-main.git', //默认是tarobaseproject,
      branch: 'mini_optimize_mainpackage', //默认是master分支，支持设置3.4.7分支
    },
    miniConfig: {
      appId: '2021002142682677',
      mcdAppId: '800017',
      alipay:
        '****************************:tinyapp/taro-car-qunar-auto-alipay.git',
    },
  },
}